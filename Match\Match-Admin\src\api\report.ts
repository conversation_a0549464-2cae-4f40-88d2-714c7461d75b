import http from '@/utils/http';
import { ReportModel, Result } from './typings';


export default class ReportService {

    public static query(data: {
        agentId?: number | string,
        branchId?: number | string,
        cashierId?: number | string,
        matchId?: number | string,
        startDate?: string,
        endDate?: string
    }) {
        return http.post<any, Result<Array<ReportModel>>>("/api/report/query", data);
    }

}