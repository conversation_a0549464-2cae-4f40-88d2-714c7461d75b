UPDATE `t_match_round`  SET
           `Result`=N'{"Number":5,"Color":0,"Sector":3,"Dozens":0,"Even":false,"Size":0}'  WHERE `Id`=N'ecded61f-f1b5-4385-bdde-cd7de04bfcc8'

UPDATE `t_order_item`  SET
            `Status` = 10   WHERE (EXISTS ( SELECT * FROM `t_order` `o`  WHERE ( `Id` = `t_order_item`.`OrderId` ) AND ( `o`.`Status` = 0 ) AND ( `o`.`RoundId` = CAST(N'ecded61f-f1b5-4385-bdde-cd7de04bfcc8' AS CHAR)) AND ( `o`.`MatchId` = CAST(N'1001' AS SIGNED)) )) AND ( `MatchId` = CAST(N'1001' AS SIGNED)) AND  (`OptionId` IN (1004,1012,1044,1048,1049,1051))  AND ( `Status` = 0 )
UPDATE `t_order_item`  SET
            `Status` = 20   WHERE (EXISTS ( SELECT * FROM `t_order` `o`  WHERE ( `Id` = `t_order_item`.`OrderId` ) AND ( `o`.`Status` = 0 ) AND ( `o`.`RoundId` = CAST(N'ecded61f-f1b5-4385-bdde-cd7de04bfcc8' AS CHAR)) AND ( `o`.`MatchId` = CAST(N'1001' AS SIGNED)) )) AND ( `MatchId` = CAST(N'1001' AS SIGNED)) AND NOT (`OptionId` IN (1004,1012,1044,1048,1049,1051))  AND ( `Status` = 0 )



            UPDATE `t_match_round`  SET
           `Result`=N'{"Number":6,"Color":1,"Sector":1,"Dozens":0,"Even":true,"Size":0}'  WHERE `Id`=N'ecded61f-f1b5-4385-bdde-cd7de04bfcc8'
SELECT `Id`,`matchName`,`Interval`,`StartTime`,`EndTime`,`Status`,`ScheduleDate` FROM `t_match`  WHERE ( `Status` = 1 )
UPDATE `t_order_item`  SET
            `Status` = 10   WHERE (EXISTS ( SELECT * FROM `t_order` `o`  WHERE ( `Id` = `t_order_item`.`OrderId` ) AND ( `o`.`Status` = 0 ) AND ( `o`.`RoundId` = CAST(N'ecded61f-f1b5-4385-bdde-cd7de04bfcc8' AS CHAR)) AND ( `o`.`MatchId` = CAST(N'1001' AS SIGNED)) )) AND ( `MatchId` = CAST(N'1001' AS SIGNED)) AND  (`OptionId` IN (1002,1013,1044,1047,1050,1051))  AND ( `Status` = 0 )
UPDATE `t_order_item`  SET
            `Status` = 20   WHERE (EXISTS ( SELECT * FROM `t_order` `o`  WHERE ( `Id` = `t_order_item`.`OrderId` ) AND ( `o`.`Status` = 0 ) AND ( `o`.`RoundId` = CAST(N'ecded61f-f1b5-4385-bdde-cd7de04bfcc8' AS CHAR)) AND ( `o`.`MatchId` = CAST(N'1001' AS SIGNED)) )) AND ( `MatchId` = CAST(N'1001' AS SIGNED)) AND NOT (`OptionId` IN (1002,1013,1044,1047,1050,1051))  AND ( `Status` = 0 )