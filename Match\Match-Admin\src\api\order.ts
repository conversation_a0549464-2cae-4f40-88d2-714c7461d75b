import http from '@/utils/http';
import { OrderModel, PaginatedList, Result } from './typings';

export default class OrderService {
    public static submit(data: {
        matchId: number,
        round: string | number,
        items: Array<{
            stake: number;
            payout: number;
            odds: number;
            optionId: number;
            optionName: string;
            type: string;
        }>
    }): Promise<Result<OrderModel>> {
        return http.post("/order/submit", data);
    }

    public static getPaginatedList(data: {
        pageIndex: number,
        pageSize: number,
        matchId?: number,
        round?: number,
        cashier?: string
    }) {
        return http.post<PaginatedList<OrderModel>>("/order/fetch", data);
    }

    public static cancel(data: {
        id: string
    }) {
        return http.post("/order/cancel", data)
    }
    public static cash(data: {
        id: string
    }) {
        return http.post("/order/cash", data)
    }

    public static query(params: { id: string }): Promise<Result<OrderModel>> {
        return http.get("/order/query", { params })
    }

    public static getByOrderNo(params: { orderNo: string }): Promise<Result<OrderModel>> {
        return http.get("/order/getByOrderNO", { params })
    }
}