<template>
    <el-dialog v-model="visible" title="店铺信息" width="600" draggable :close-on-click-modal="false"
        :close-on-press-escape="false" @closed="reset">

        <el-form label-width="100px" ref="form" :model="formData" :show-message="false">
            <el-form-item label="店铺名称" required prop="branchName">
                <el-input placeholder="" v-model="formData.branchName" />
            </el-form-item>
            <el-form-item label="代理" prop="contactName">
                <el-input placeholder="" v-model="formData.contactName" />
            </el-form-item>
            <el-form-item label="省份" prop="address">
                <el-input placeholder="" v-model="formData.address" />
            </el-form-item>
            <el-form-item label="所属代理商" required prop="accountId">
                <el-select placeholder="" clearable v-model="formData.accountId">
                    <el-option v-for="item in agents" :label="item.nickName" :value="item.id"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="状态">
                <el-radio-group v-model="formData.status">
                    <el-radio :value="10">启用</el-radio>
                    <el-radio :value="20">禁用</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="justify-center">
                <el-button type="danger" :disabled="loading" plain @click="close">取消</el-button>
                <el-button type="primary" :disabled="loading" @click="save">保存</el-button>
            </div>
        </template>
    </el-dialog>
</template>



<script lang="ts" setup>
import AccountService from '@/api/account';
import BranchService from '@/api/branch';
import { BranchModel } from '@/api/typings';
import { EAccountType } from '@/typings/enum';


const loading = ref(false);
const visible = ref(false);


const emits = defineEmits(['saved']);

const formData = ref<BranchModel>({
    status: 10
} as BranchModel);


const form = useTemplateRef('form');
const save = () => {
    //表单验证
    form.value?.validate((valid, _fields) => {
        if (!valid) {
            loading.value = false;
            return;
        }
        loading.value = true;
        BranchService.create(formData.value).then(res => {
            visible.value = false;
            emits('saved', formData.value);
        }).finally(() => {
            loading.value = false;
        });
    })

}

const reset = () => {
    form.value?.resetFields();
}

const open = () => {
    visible.value = true;
}

const close = () => {
    visible.value = false;
}


const agents = ref<Array<any>>([]);
const getAgents = () => {
    AccountService.getPaginatedList({ pageIndex: 1, pageSize: 9999, accountType: EAccountType.Agent }).then(res => {
        agents.value = res.items;
    });
}

onMounted(() => {
    getAgents();
})

defineExpose({
    open: open
});

</script>