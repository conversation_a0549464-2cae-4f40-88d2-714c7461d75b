!function(){function e(e,t,o,n,i,r,l){try{var a=e[r](l),s=a.value}catch(e){return void o(e)}a.done?t(s):Promise.resolve(s).then(n,i)}System.register(["./.pnpm-legacy-DHcv0MF7.js"],(function(t,o){"use strict";var n,i,r,l,a,s,d,c,p,h,u,m,f,g,x,y;return{setters:[e=>{n=e.d,i=e.o,r=e.c,l=e.r,a=e.w,s=e.a,d=e.E,c=e.b,p=e.e,h=e.f,u=e.g,m=e.h,f=e.s,g=e.n,x=e.i,y=e.j}],execute:function(){var b=document.createElement("style");b.textContent="html,body,#app{height:100vh;margin:0;padding:0}body{margin:0;padding:0;font-family:Source Sans Pro,Helvetica Neue,Helvetica,Arial,sans-serif;font-weight:400;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-size:1em;line-height:1.42857143;min-width:400px}body .el-message-box{--el-messagebox-width: 300px}.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6{font-family:inherit;font-weight:400;font-size:100%;color:inherit;margin:0}.full-width{width:100%}*,:after{box-sizing:border-box}.full-page{width:100%;height:100vh}.page-body{margin:10px;display:flex}a{background:transparent;outline:none;transition:color .3s ease}.text-right{text-align:right}.text-bold{font-weight:700}.flex-col{display:flex;flex-direction:column}.flex-row{display:flex;flex-direction:row}.justify-start{display:flex;justify-content:flex-start}.justify-center{display:flex;justify-content:center}.justify-end{display:flex;justify-content:flex-end}.justify-evenly{display:flex;justify-content:space-evenly}.justify-around{display:flex;justify-content:space-around}.justify-between{display:flex;justify-content:space-between}.align-start{display:flex;align-items:flex-start}.align-center{display:flex;align-items:center}.align-end{display:flex;align-items:flex-end}.panel{position:relative;background:#fff;margin-bottom:5px;box-shadow:0 1px 1px rgba(0,0,0,.05)}.panel .panel-header{color:#444;display:block;padding:10px;position:relative;border-bottom:1px solid #f1f1f1;min-height:50px;display:flex;align-items:center;justify-content:space-between}.panel .panel-header.with-border{border-bottom:1px solid #f1f1f1}.panel .panel-header .panel-title{display:inline-block;font-size:18px;margin:0;line-height:1}.panel .panel-body{padding:5px 10px}.panel .panel-footer{padding:5px 10px;border-top:1px solid #f1f1f1}.sector,.exact-number,.dozens,.other{display:flex;flex-direction:row}.sector .item,.exact-number .item,.dozens .item,.other .item{width:60px;height:30px;color:#fff;text-align:center;line-height:30px;border-radius:3px;background-color:#635d5d;font-size:14px;margin-right:10px}.forecast{gap:10px;flex-wrap:wrap;width:400px}.forecast .item{margin-right:0!important;margin-bottom:0}.exact-number .number{display:flex;flex-direction:column;flex-wrap:wrap;height:148px}.exact-number .zero .item{line-height:148px;height:148px}.exact-number .item{width:40px;height:35px;margin-right:2px;margin-bottom:2px;font-size:16px;line-height:35px}.dozens .item{width:130px;margin-right:10px}.other{margin-top:5px}.other .item{width:60px;margin-right:10px}.item{cursor:pointer;user-select:none}.item:hover,.item.active{background-color:red!important}.item.green{background-color:#1cd41c}.item.red{background-color:#9b1e24}.item.black{background-color:#000}.item.gray{background-color:#635d5d}.search-form.el-form--inline .el-form-item{margin-bottom:5px;width:220px;margin-right:10px}\n/*$vite$:1*/",document.head.appendChild(b);const w=n({__name:"App",setup:e=>(e,t)=>{const o=l("router-view"),n=l("OrderDialog"),c=d;return i(),r(c,null,{default:a((()=>[(i(),r(o,{key:e.$route.fullPath})),s(n,{ref:"orderDialog"},null,512)])),_:1})}}),v=function(e,t,o){let n=Promise.resolve();function i(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return n.then((t=>{for(const e of t||[])"rejected"===e.status&&i(e.reason);return e().catch(i)}))},k=[{path:"/",name:"",redirect:"/dashboard",component:()=>v((()=>o.import("./layout-legacy-qdfGrToV.js"))),children:[{path:"dashboard",name:"dashboard",component:()=>v((()=>o.import("./dashboard-legacy-BGldCFIo.js")))},{path:"assets",name:"assets",component:()=>v((()=>o.import("./assets-legacy-CwpiSpDB.js")))},{path:"report",name:"report",component:()=>v((()=>o.import("./report-legacy-B3rV5lh4.js")))},{path:"account",name:"account",component:()=>v((()=>o.import("./account-legacy-YDj6n3OL.js")))},{path:"branch",name:"branch",component:()=>v((()=>o.import("./branch-legacy-AawN25HT.js")))},{path:"match",name:"match",component:()=>v((()=>o.import("./match-legacy-BYqCx_H8.js")))}]},{path:"/login",name:"login",component:()=>v((()=>o.import("./login-legacy-CBTU1uey.js")))}],j=t("T","MATCH.SUPER.Token"),C=t("r",c({history:p(),routes:k,scrollBehavior:(e,t,o)=>({top:0})}));if(C.beforeEach(function(){var t,o=(t=function*(e,t,o){const n=h.get(j);n&&"/login"==e.path?o("/"):n||"/login"==e.path?o():o("/login")},function(){var o=this,n=arguments;return new Promise((function(i,r){var l=t.apply(o,n);function a(t){e(l,i,r,a,s,"next",t)}function s(t){e(l,i,r,a,s,"throw",t)}a(void 0)}))});return function(e,t,n){return o.apply(this,arguments)}}()),"undefined"!=typeof window){function B(){var e=document.body,t=document.getElementById("__svg__icons__dom__");t||((t=document.createElementNS("http://www.w3.org/2000/svg","svg")).style.position="absolute",t.style.width="0",t.style.height="0",t.id="__svg__icons__dom__",t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink")),t.innerHTML='<symbol viewBox="0 0 48 48" fill="none"  id="icon-close"><path d="M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Z" stroke="currentColor" stroke-width="4" stroke-linejoin="round" /><path d="M29.657 18.343 18.343 29.657M18.343 18.343l11.314 11.314" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" /></symbol><symbol viewBox="0 0 48 48" fill="none"  id="icon-home"><path d="M9 18v24h30V18L24 6 9 18Z" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" /><path d="M19 29v13h10V29H19Z" stroke="#333" stroke-width="4" stroke-linejoin="round" /><path d="M9 42h30" stroke="#333" stroke-width="4" stroke-linecap="round" /></symbol><symbol viewBox="0 0 48 48" fill="none"  id="icon-order"><path d="M33.05 7H38a2 2 0 0 1 2 2v33a2 2 0 0 1-2 2H10a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h7v3h14V7h2.05Z" stroke="currentColor" stroke-width="4" stroke-linejoin="round" /><path stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M17 4h14v6H17zM27 19l-8 8.001h10.004l-8.004 8" /></symbol><symbol viewBox="0 0 48 48" fill="none"  id="icon-power"><path d="M14.5 8a19.05 19.05 0 0 0-4.75 3.84C6.794 15.146 5 19.49 5 24.245 5 34.603 13.507 43 24 43s19-8.397 19-18.755c0-4.756-1.794-9.099-4.75-12.405A19.02 19.02 0 0 0 33.5 8M24 4v20" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" /></symbol><symbol viewBox="0 0 48 48"  id="icon-setting"><path d="M18.284 43.171a19.995 19.995 0 0 1-8.696-5.304 6 6 0 0 0-5.182-9.838A20.09 20.09 0 0 1 4 24c0-2.09.32-4.106.916-6H5a6 6 0 0 0 5.385-8.65 19.968 19.968 0 0 1 8.267-4.627A6 6 0 0 0 24 8a6 6 0 0 0 5.348-3.277 19.968 19.968 0 0 1 8.267 4.627A6 6 0 0 0 43.084 18c.595 1.894.916 3.91.916 6 0 1.38-.14 2.728-.406 4.03a6 6 0 0 0-5.182 9.838 19.995 19.995 0 0 1-8.696 5.303 6.003 6.003 0 0 0-11.432 0Z" fill="none" stroke="currentColor" stroke-width="4" stroke-linejoin="round" /><path d="M24 31a7 7 0 1 0 0-14 7 7 0 0 0 0 14Z" fill="none" stroke="#333" stroke-width="4" stroke-linejoin="round" /></symbol>',e.insertBefore(t,e.lastChild)}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",B):B()}const S=u({locale:localStorage.getItem("lang")||"en",globalInjection:!0,legacy:!1}),_=m();_.use(f);var A=t("a",(e=>(e[e.LuckyRoulette=1e3]="LuckyRoulette",e[e.VirtualRace=2e3]="VirtualRace",e[e.ColorLucky=3e3]="ColorLucky",e))(A||{})),M=(e=>(e[e.Submitted=0]="Submitted",e[e.UnSettled=10]="UnSettled",e[e.WinPart=11]="WinPart",e[e.Lose=20]="Lose",e[e.Canceled=30]="Canceled",e[e.Settled=40]="Settled",e))(M||{}),E=t("b",(e=>(e[e.Normal=10]="Normal",e[e.Disabled=20]="Disabled",e))(E||{})),z=t("E",(e=>(e[e.Admin=10]="Admin",e[e.Agent=20]="Agent",e[e.Cashier=30]="Cashier",e))(z||{}));const L=[{type:M,value:M.Submitted,color:"#000",text:"Submitted"},{type:M,value:M.Settled,color:"#2CF886",text:"Settled"},{type:M,value:M.UnSettled,color:"#EAB3FE",text:"UnSettled"},{type:M,value:M.Lose,color:"#999",text:"Lose"},{type:M,value:M.Canceled,color:"#ba2106",text:"Canceled"},{type:z,value:z.Admin,color:"#000",text:"Admin"},{type:z,value:z.Agent,color:"#000",text:"Agent"},{type:z,value:z.Cashier,color:"#000",text:"Cashier"}],H={mounted(e,t){P(e,t)},updated(e,t){P(e,t)}},P=(e,t)=>{const{type:o,value:n,bold:i=!0}=null==t?void 0:t.value;if(void 0===n||null==n)return;let r=L.find((e=>e.type==o&&e.value==n));var l;null!=r?(e.style.color=r.color,e.style.fontWeight=i?"bold":"normal",e.innerText=S.global.t(null!==(l=r.text)&&void 0!==l?l:o[n])):e.innerText=""};y(w).use(C).use(_).use(S).use((function(e,{locale:t="en"}={}){g.locale(t),e.config.globalProperties.$numeral=(e,t="0,0")=>g(e).format(t)})).use((function(e,{locale:t="en"}={}){x.locale(t),e.config.globalProperties.$moment=(e,t="YYYY/MM/DD HH:mm")=>x(e).format(t)})).use((function(e){e.directive("enum",H)})).mount("#app")}}}))}();
