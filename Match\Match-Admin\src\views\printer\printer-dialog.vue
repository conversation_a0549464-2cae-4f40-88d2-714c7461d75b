<template>
    <el-dialog v-model="visible" title="Printer Setting" width="300" draggable append-to-body
        :close-on-click-modal="false" :close-on-press-escape="false">
        <el-form label-position="top" label-width="auto" :model="printer" class="printer" v-loading="loading"
            require-asterisk-position="right">
            <el-form-item label="Mode">
                <el-select placeholder=""  v-model="printer.type" @change="onPrinterTypeChange">
                    <el-option label="" value="" />
                    <el-option label="Bluetooth" value="bluetooth" />
                    <el-option label="Network" value="tcp" />
                    <el-option label="USB" value="usb" />
                </el-select>
            </el-form-item>
            <template v-if="printer.type == 'tcp'">
                <el-form-item label="IP" required>
                    <el-input placeholder="" v-model="printer.address"></el-input>
                </el-form-item>
                <el-form-item label="Port" required>
                    <el-input placeholder="" v-model.number="printer.port"></el-input>
                </el-form-item>
            </template>
            <template v-if="printer.type == 'bluetooth'">
                <el-form-item label="Bluetooth Printers">
                    <el-select placeholder="" v-model="printer.address" @change="onPrinterChange">
                        <el-option v-for="item in bluetoothPrinters" :label="item.name" :value="item.address" />
                    </el-select>
                </el-form-item>
            </template>
            <template v-if="printer.type == 'usb'">
                <el-form-item label="USB Printers">
                    <el-select placeholder="" v-model="printer.id" @change="onPrinterChange">
                        <el-option v-for="item in usbPrinters" :label="item.productName" :value="item.deviceId" />
                    </el-select>
                </el-form-item>
            </template>
            <el-form-item label="Paper Width">
                <el-radio-group v-model="printer.paperWidth">
                    <el-radio :value="72">80 mm</el-radio>
                    <!-- <el-radio :value="48">58 mm</el-radio> -->
                </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="Paper Width in mm">
                <el-input placeholder="" v-model.number="printer.printerWidthMM"></el-input>
            </el-form-item>
            <el-form-item label="Number of characters per line">
                <el-input placeholder="" v-model.number="printer.printerNbrCharactersPerLine"></el-input>
            </el-form-item> -->

            <div class="justify-between">
                <el-button type="warning" @click="printTestPage">Print Test Page</el-button>
                <el-button type="primary" @click="savePrinter" :disabled="isSaveButtonDisabled">Save</el-button>
            </div>
        </el-form>

    </el-dialog>
</template>

<script lang="ts" setup>
import { PrinterModel } from '@/api/typings';
import { useGlobalStore } from '@/store/global';
import { formatDate } from '@/utils';
import { cloneDeep } from 'lodash';
import { storeToRefs } from 'pinia';
import { ref, watch, computed } from 'vue';

const globalStore = useGlobalStore();
const { printerModel } = storeToRefs(globalStore);

// 修改: 使用 props 接收 v-model 的值，并通过 emit 更新
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:modelValue']);

const visible = ref(props.modelValue);

watch(() => props.modelValue, (newVal) => {
    visible.value = newVal;
});

watch(visible, (newVal) => {
    emit('update:modelValue', newVal);
});

const printer = ref<PrinterModel>({
    type: '',
    name: '',
    address: '',
    port: 9100,
    id: '',
    printerWidthMM: 72,
    printerNbrCharactersPerLine: 48,
    paperWidth: 72
});

//蓝牙设备
const bluetoothPrinters = ref<Array<{
    address: string;
    boundState: number;
    deviceClass: number;
    features: string;
    majorDeviceClass: number;
    name: string;
    type: number;
}>>([]);


//USB设备
const usbPrinters = ref<Array<{
    deviceId: number;
    manufacturerName: string;
    productName: string;
    vendorId: number;
}>>([]);

const loading = ref(false);

const onPrinterChange = (val: any) => {
    if (printer.value.type == 'bluetooth') {
        let item = bluetoothPrinters.value.find(item => item.address == val);
        printer.value.name = item?.name;
    }
    if (printer.value.type == 'usb') {
        let item = usbPrinters.value.find(item => item.deviceId == val);
        printer.value.name = item?.productName;
    }
}

const savePrinter = () => {
    //ID of printer to find (Bluetooth: address, TCP: Use address + port instead, USB: deviceId)
    if (printer.value.type == 'tcp') {
        if (!printer.value.address || !printer.value.port) {
            return;
        }
        printer.value.id = printer.value.address + ':' + printer.value.port;
    }

    if (printer.value.type == 'bluetooth') {
        if (!printer.value.address) {
            return;
        }
        printer.value.id = printer.value.address;
    }

    if (printer.value.type == 'usb') {
        if (!printer.value.id) {
            return;
        }
    }

    globalStore.printerModel = cloneDeep(printer.value);

    visible.value = false;
}

const isSaveButtonDisabled = computed(() => {
    if (!printer.value.type) return true; // 如果未选择类型，禁用保存按钮
    if (printer.value.type === 'tcp') {
        return !printer.value.address || !printer.value.port; // TCP 类型需要同时填写 IP 和端口
    }
    if (printer.value.type === 'bluetooth') {
        return !printer.value.address; // 蓝牙类型需要选择打印机地址
    }
    if (printer.value.type === 'usb') {
        return !printer.value.id; // USB 类型需要选择打印机 ID
    }
    return true; // 默认情况下禁用
});

const onPrinterTypeChange = (val: any) => {

    printer.value.type = val;
    printer.value.name = '';
    printer.value.address = '';
    printer.value.id = '';

    if (val == 'tcp' || val == '') {
        return;
    }

    try {
        // loading.value = true;
        window.ThermalPrinter.listPrinters({ type: val }, (printers: any) => {
            if (val == 'bluetooth') {
                bluetoothPrinters.value = printers;
            }
            if (val == 'usb') {
                usbPrinters.value = printers;
            }
            loading.value = false;
        }, (error: any) => {
            //TODO
            console.error('Ups, we cant list the printers!', error);
            loading.value = false;
        })
    } catch (ex) {

    } finally {
        // loading.value = false;
    }
}


const printTestPage = () => {
    if (!window.ThermalPrinter) {
        return;
    }

    if (printer.value.type == 'bluetooth') {
        printBluetoothTestPage();
    }

    if (printer.value.type == 'usb') {
        printUsbTestPage();
    }
}


const getText = () => {

    let text = '';
    text += `[C]<u><font size='big'>MAGIC ORDER[TEST]</font></u>\n`;
    text += `[L]\n`;
    text += `[L]<b>Branch: </b>Test Branch\n`;
    text += `[L]<b>Cashier: </b>cashier\n`;
    text += `[L]<b>Barcode: </b>00000000000000\n`;
    // text += `[L]<b>Round: </b>${data.round}\n`;
    text += `[L]<b>Date: </b>${formatDate(new Date())}\n`;
    // text += `[L]<b>Valid Until: </b>\n`;
    text += `[C]================================================\n`;
    text += `[L]Test [R]Round: 000\n`;
    text += `[L]<b>WIN: 0 </b>[R]Odds:  0\n`;
    text += `[R]Stake: 0  Payout: 0\n`;
    text += `[C]------------------------------------------------\n`;
    text += `[L][L]<b>Stake</b>[R]<b>TZS 0</b>\n`;
    text += `[L][L]<b>Max Payout</b>[R]<b>TZS  0</b>\n`;
    text += `[C]================================================\n`;
    text += `[L]\n`;
    text += `[C]<barcode type='128' height='10'>00000000000000</barcode>\n`;
    text += `[L]\n`;
    text += `[L]Bet responsibly\n`;
    text += `[L]\n`;
    text += `[L]\n`;
    text += `[L]\n`;
    text += `[L]\n`;
    return text;

}

const printBluetoothTestPage = () => {

    if (!printer.value.address) {
        return;
    }

    const data = {
        type: printer.value.type,
        text: getText(),             //打印内容
        address: printer.value.address,
        id: printer.value.address,            //'DC:0D:30:20:61:60',
        printerWidthMM: 72,                 //纸张宽度 （mm）
        // mmFeedPaper: 120,                //末端毫米距离进纸
        dotsFeedPaper: 50,                   //末端进纸的距离
        printerNbrCharactersPerLine: 48     //每行字符数 80MM的最大48字符
    };

    window.ThermalPrinter.printFormattedTextAndCut(data, res => {


        console.log(res);
    }, error => {
        console.error(error);
    });
}
const printUsbTestPage = () => {

    const usbPrinter = usbPrinters.value.find(p => p.deviceId == printer.value.id);

    console.log('usbPrinter', usbPrinter);
    if(!usbPrinter){
        return;
    }

    const data = {
        type: 'usb',
        text: getText(),             //打印内容
        // address: printer.value.address,
        id:1005,            //'DC:0D:30:20:61:60',
        printerWidthMM: 72,                 //纸张宽度 （mm）
        // mmFeedPaper: 120,                //末端毫米距离进纸
        dotsFeedPaper: 0,                   //末端进纸的距离
        printerNbrCharactersPerLine: 48     //每行字符数 80MM的最大48字符
    };
    console.log('data', data);

        window.ThermalPrinter.printFormattedText(data, res => {


            console.log(res);
        }, error => {
            console.error(error);
        });

    window.ThermalPrinter.requestPermissions(usbPrinter, () => {


    }, (err) => {
        console.error('Permission denied - We can\'t print!', err);
    })
}


</script>

<style lang="scss" scoped>
.printer {
    min-height: 200px;

    .el-form-item {
        margin-bottom: 5px;
    }

    :deep(.el-form-item__label) {
        margin-bottom: 0px;
    }
}
</style>