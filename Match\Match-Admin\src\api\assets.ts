import http from '@/utils/http';
import { AccountModel, AssetsLogModel, PaginatedList, Result } from './typings';


export default class AssetsService {

    public static getPaginatedList(data: {
        pageIndex: number,
        pageSize: number
    }) {
        return http.post<any, PaginatedList<AssetsLogModel>>("/api/assets/fetch", data);
    }

    public static transfer(data: AssetsLogModel) {
        return http.post<any, Result<AssetsLogModel>>("/api/assets/transfer", data);
    }

}