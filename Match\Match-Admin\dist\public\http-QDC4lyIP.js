import{T as c,r as n}from"./index-Cc7WJN9q.js";import{a4 as d,f as u,a5 as r,a6 as l,a7 as m}from"./.pnpm-DuVJJfpW.js";const f={401:e=>{r.error(e.data.message),n.replace("/login")},403:e=>{r.error(e.data.message)},409:e=>{r.error(e.data.message)},500:e=>{r.error(e.data.message)}},s={401:e=>{n.replace({path:"/login"})},403:e=>{r.error("没有权限访问")},404:e=>{r.error("请求的资源不存在")},500:e=>{r.error("服务器内部错误")},undefined:e=>{m({title:"Error",message:e.message,type:"error"})}},i=d.create({baseURL:"http://************:9002",timeout:1e4});i.interceptors.request.use(e=>{if(e.skipRequestInterceptor)return e;const t=u.get(c);return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>(console.log("request error",e),Promise.reject(e)));i.interceptors.response.use(e=>{if(e.config.skipResponseInterceptor||e.data instanceof Blob)return e.data;const{code:t}=e.data;if(t===200)return e.data;const a=f[t];return a?a(e):r.error(e.data.message||"未知业务错误"),Promise.reject(e)},e=>{var o;if(l(e))return console.log("Request canceled",e.message),Promise.reject(e);const t=(o=e.response)==null?void 0:o.status;return(s[t]||s.undefined)(e),Promise.reject(e)});export{i as h};
