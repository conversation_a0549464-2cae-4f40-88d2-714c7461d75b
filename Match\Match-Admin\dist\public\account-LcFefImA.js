import{d as O,z as N,A as q,B as j,o as i,c as y,w as l,p as v,a as e,v as r,u as o,l as U,C as h,F as P,R as $,D as le,G,H as W,J as H,V as ae,W as oe,L as J,x as Q,M as te,N as X,O as K,P as ee,X as de,Q as re,t as L,Y as ie,S as pe,Z as me}from"./.pnpm-DuVJJfpW.js";import{_ as ce}from"./io-table-DC_2HcLS.js";import{A as Y,B as ne}from"./branch-zzdSpi4p.js";import{b as se,E as z}from"./index-Cc7WJN9q.js";import{a as ue,g as fe}from"./option-CWv3DsyR.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./http-QDC4lyIP.js";const _e={class:"justify-center"},be=O({__name:"detail-module",emits:["saved"],setup(Z,{expose:D,emit:m}){const k=ue(z),b=N(!1),p=N(!1),R=m,s=N({status:se.Normal}),T=q("form"),S=()=>{var t;(t=T.value)==null||t.validate((a,f)=>{a&&(b.value=!0,Y.update(s.value).then(C=>{p.value=!1,R("saved",s.value)}).finally(()=>{b.value=!1}))})},B=()=>{var t;(t=T.value)==null||t.resetFields()},I=t=>{p.value=!0,s.value=t},E=()=>{p.value=!1},g=N([]),d=()=>{ne.getPaginatedList({pageIndex:1,pageSize:9999}).then(t=>{g.value=t.items})};return j(()=>{d()}),D({open:I}),(t,a)=>{const f=X,C=G,c=W,V=H,x=ae,_=oe,M=J,w=Q,F=te;return i(),y(F,{modelValue:o(p),"onUpdate:modelValue":a[5]||(a[5]=n=>le(p)?p.value=n:null),title:"账号信息",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1,onClosed:B},{footer:l(()=>[v("div",_e,[e(w,{type:"danger",disabled:o(b),plain:"",onClick:E},{default:l(()=>a[8]||(a[8]=[r("取消")])),_:1},8,["disabled"]),e(w,{type:"primary",disabled:o(b),onClick:S},{default:l(()=>a[9]||(a[9]=[r("保存")])),_:1},8,["disabled"])])]),default:l(()=>[e(M,{"label-width":"100px",model:o(s),ref_key:"form",ref:T,"show-message":!1},{default:l(()=>[e(c,{label:"账号类型",prop:"accountType",required:""},{default:l(()=>[e(C,{modelValue:o(s).accountType,"onUpdate:modelValue":a[0]||(a[0]=n=>o(s).accountType=n),placeholder:"",clearable:""},{default:l(()=>[(i(!0),U(P,null,h(o(k),n=>(i(),y(f,{label:n.label,value:n.value},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),o(s).accountType==30?(i(),y(c,{key:0,label:"所属店铺",prop:"branchId",required:""},{default:l(()=>[e(C,{modelValue:o(s).branchId,"onUpdate:modelValue":a[1]||(a[1]=n=>o(s).branchId=n),placeholder:"",clearable:""},{default:l(()=>[(i(!0),U(P,null,h(o(g),n=>(i(),y(f,{label:n.branchName,value:n.id},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1})):$("",!0),e(c,{label:"用户名",prop:"loginAccount",required:""},{default:l(()=>[e(V,{placeholder:"",modelValue:o(s).loginAccount,"onUpdate:modelValue":a[2]||(a[2]=n=>o(s).loginAccount=n),readonly:""},null,8,["modelValue"])]),_:1}),e(c,{label:"昵称",prop:"nickName",required:""},{default:l(()=>[e(V,{placeholder:"",modelValue:o(s).nickName,"onUpdate:modelValue":a[3]||(a[3]=n=>o(s).nickName=n)},null,8,["modelValue"])]),_:1}),e(c,{label:"状态"},{default:l(()=>[e(_,{modelValue:o(s).status,"onUpdate:modelValue":a[4]||(a[4]=n=>o(s).status=n)},{default:l(()=>[e(x,{value:10},{default:l(()=>a[6]||(a[6]=[r("启用")])),_:1}),e(x,{value:20},{default:l(()=>a[7]||(a[7]=[r("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}}),ve={class:"justify-center"},ge=O({__name:"create-module",emits:["saved"],setup(Z,{expose:D,emit:m}){const k=ue(z),b=N(!1),p=N(!1),R=m,s=N({status:se.Normal}),T=q("form"),S=()=>{var t;(t=T.value)==null||t.validate((a,f)=>{a&&(b.value=!0,Y.create(s.value).then(C=>{p.value=!1,R("saved",s.value)}).finally(()=>{b.value=!1}))})},B=()=>{var t;(t=T.value)==null||t.resetFields()},I=()=>{p.value=!0},E=()=>{p.value=!1},g=N([]),d=()=>{ne.getPaginatedList({pageIndex:1,pageSize:9999}).then(t=>{g.value=t.items})};return j(()=>{d()}),D({open:I}),(t,a)=>{const f=X,C=G,c=W,V=H,x=ae,_=oe,M=J,w=Q,F=te;return i(),y(F,{modelValue:o(p),"onUpdate:modelValue":a[6]||(a[6]=n=>le(p)?p.value=n:null),title:"账号信息",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1,onClosed:B},{footer:l(()=>[v("div",ve,[e(w,{type:"danger",disabled:o(b),plain:"",onClick:E},{default:l(()=>a[9]||(a[9]=[r("取消")])),_:1},8,["disabled"]),e(w,{type:"primary",disabled:o(b),onClick:S},{default:l(()=>a[10]||(a[10]=[r("保存")])),_:1},8,["disabled"])])]),default:l(()=>[e(M,{"label-width":"100px",model:o(s),ref_key:"form",ref:T,"show-message":!1},{default:l(()=>[e(c,{label:"账号类型",prop:"accountType",required:""},{default:l(()=>[e(C,{modelValue:o(s).accountType,"onUpdate:modelValue":a[0]||(a[0]=n=>o(s).accountType=n),placeholder:"",clearable:""},{default:l(()=>[(i(!0),U(P,null,h(o(k),n=>(i(),y(f,{label:n.label,value:n.value},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),o(s).accountType==30?(i(),y(c,{key:0,label:"所属店铺",prop:"branchId",required:""},{default:l(()=>[e(C,{modelValue:o(s).branchId,"onUpdate:modelValue":a[1]||(a[1]=n=>o(s).branchId=n),placeholder:"",clearable:""},{default:l(()=>[(i(!0),U(P,null,h(o(g),n=>(i(),y(f,{label:n.branchName,value:n.id},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1})):$("",!0),e(c,{label:"用户名",prop:"loginAccount",required:""},{default:l(()=>[e(V,{placeholder:"",modelValue:o(s).loginAccount,"onUpdate:modelValue":a[2]||(a[2]=n=>o(s).loginAccount=n),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(c,{label:"密码",prop:"loginPassword",required:""},{default:l(()=>[e(V,{placeholder:"",type:"password",modelValue:o(s).loginPassword,"onUpdate:modelValue":a[3]||(a[3]=n=>o(s).loginPassword=n),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(c,{label:"昵称",prop:"nickName",required:""},{default:l(()=>[e(V,{placeholder:"",modelValue:o(s).nickName,"onUpdate:modelValue":a[4]||(a[4]=n=>o(s).nickName=n)},null,8,["modelValue"])]),_:1}),e(c,{label:"状态"},{default:l(()=>[e(_,{modelValue:o(s).status,"onUpdate:modelValue":a[5]||(a[5]=n=>o(s).status=n)},{default:l(()=>[e(x,{value:10},{default:l(()=>a[7]||(a[7]=[r("启用")])),_:1}),e(x,{value:20},{default:l(()=>a[8]||(a[8]=[r("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}}),Ve={class:"page-body flex-col"},ye={class:"panel"},ke={class:"panel-body"},Ne={class:"panel"},Te={class:"panel-header with-border"},Ce={class:"panel-tools"},Ae={class:"panel-body",style:{padding:"0"}},De=O({__name:"account",setup(Z){const D=fe(z),m=K({loginAccount:"",nickName:"",branchName:"",accountType:""}),k=K({pageIndex:1,pageSize:15,totalCount:0,totalPages:0}),b=()=>{k.pageIndex=1,g()},p=q("searchForm"),R=()=>{var d;(d=p.value)==null||d.resetFields(),b()},s=q("DetailModuleRef"),T=d=>{var t;(t=s.value)==null||t.open(ie.cloneDeep(d))},S=q("CreateModuleRef"),B=()=>{var d;(d=S.value)==null||d.open()},I=N([]),E=N(!1),g=()=>{E.value=!0,Y.getPaginatedList({...m,...k}).then(d=>{I.value=d.items,k.pageIndex=d.pageIndex,k.totalCount=d.totalCount,k.totalPages=d.totalPages}).finally(()=>{E.value=!1})};return j(()=>{g()}),(d,t)=>{const a=H,f=W,C=X,c=G,V=Q,x=J,_=pe,M=me,w=ce,F=de("enum"),n=re;return i(),U(P,null,[v("div",Ve,[v("div",ye,[t[6]||(t[6]=v("div",{class:"panel-header with-border"},[v("div",{class:"panel-title"},"搜索条件")],-1)),v("div",ke,[e(x,{model:o(m),inline:!0,ref_key:"searchForm",ref:p,class:"search-form"},{default:l(()=>[e(f,{label:"用户名",prop:"loginAccount"},{default:l(()=>[e(a,{placeholder:"",modelValue:o(m).loginAccount,"onUpdate:modelValue":t[0]||(t[0]=u=>o(m).loginAccount=u),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(f,{label:"昵称",prop:"nickName"},{default:l(()=>[e(a,{placeholder:"",modelValue:o(m).nickName,"onUpdate:modelValue":t[1]||(t[1]=u=>o(m).nickName=u),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(f,{label:"所属店铺",prop:"branchName"},{default:l(()=>[e(a,{placeholder:"",modelValue:o(m).branchName,"onUpdate:modelValue":t[2]||(t[2]=u=>o(m).branchName=u),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(f,{label:"账号类型",prop:"accountType"},{default:l(()=>[e(c,{modelValue:o(m).accountType,"onUpdate:modelValue":t[3]||(t[3]=u=>o(m).accountType=u),placeholder:"",clearable:""},{default:l(()=>[(i(!0),U(P,null,h(o(D),u=>(i(),y(C,{label:u.label,value:u.value},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),e(f,null,{default:l(()=>[e(V,{type:"primary",onClick:b},{default:l(()=>t[4]||(t[4]=[r("搜索")])),_:1}),e(V,{type:"danger",plain:"",onClick:R},{default:l(()=>t[5]||(t[5]=[r("重置")])),_:1})]),_:1})]),_:1},8,["model"])])]),v("div",Ne,[v("div",Te,[t[8]||(t[8]=v("div",{class:"panel-title"},"账号列表",-1)),v("div",Ce,[e(V,{type:"primary",onClick:B},{default:l(()=>t[7]||(t[7]=[r("添加")])),_:1})])]),ee((i(),U("div",Ae,[e(w,{height:"500",data:o(I),pagination:o(k),onPagerChange:g},{default:l(()=>[e(_,{prop:"id",label:"序号"},{default:l(({row:u,$index:A})=>[r(L(A+1),1)]),_:1}),e(_,{prop:"",label:"",fixed:"right"},{default:l(({row:u})=>[e(V,{plain:"",type:"primary",size:"small",onClick:A=>T(u)},{default:l(()=>t[9]||(t[9]=[r("编辑")])),_:2},1032,["onClick"])]),_:1}),e(_,{prop:"loginAccount",label:"用户名"}),e(_,{prop:"nickName",label:"昵称"}),e(_,{prop:"branch",label:"所属店铺"},{default:l(({row:u})=>{var A;return[r(L((A=u.branch)==null?void 0:A.branchName),1)]}),_:1}),e(_,{prop:"accountType",label:"账号类型"},{default:l(({row:u})=>[ee(v("span",null,null,512),[[F,{type:o(z),value:u.accountType}]])]),_:1}),e(_,{prop:"assets.balance",label:"余额"},{default:l(({row:u})=>{var A;return[r(L(d.$numeral((A=u.assets)==null?void 0:A.balance)),1)]}),_:1}),e(_,{prop:"status",label:"状态"},{default:l(({row:u})=>[u.status==10?(i(),y(M,{key:0,type:"success"},{default:l(()=>t[10]||(t[10]=[r("启用")])),_:1})):$("",!0),u.status==20?(i(),y(M,{key:1,type:"danger"},{default:l(()=>t[11]||(t[11]=[r("禁用")])),_:1})):$("",!0)]),_:1}),e(_,{prop:"createTime",label:"添加时间"},{default:l(({row:u})=>[r(L(d.$moment(u.createTime)),1)]),_:1})]),_:1},8,["data","pagination"])])),[[n,o(E)]])])]),e(be,{ref:"DetailModuleRef",onSaved:g},null,512),e(ge,{ref:"CreateModuleRef",onSaved:g},null,512)],64)}}});export{De as default};
