import { PrinterModel, ProfileModel, TokenModel } from "@/api/typings";
import UserService from "@/api/user";
import { TokenKey } from "@/constants";
import router from "@/router";
import Cookies from "js-cookie";
import { defineStore } from "pinia";

export const useGlobalStore = defineStore('globalStore', () => {

    const tokenModel = ref<TokenModel>();
    const profileModel = ref<ProfileModel>();
    const printerModel = ref<PrinterModel>();


    const userLogin = async (formData: { loginAccount: string; loginPassword: string }) => {
        const { result } = await UserService.login(formData);

        tokenModel.value = {
            token: result.token,
            expireAt: result.expireAt
        }
        profileModel.value = result;

        Cookies.set(TokenKey, result.token, { expires: new Date(result.expireAt) });

        router.push('/');
    }

    const userLogout = () => {
        tokenModel.value = undefined;
        profileModel.value = undefined;
        Cookies.remove(TokenKey);
        router.replace('/login');
    }

    return { tokenModel, profileModel, userLogout, userLogin, printerModel }

}, { persist: { key: 'globalStore', storage: window.localStorage } })