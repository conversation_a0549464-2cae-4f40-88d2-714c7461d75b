{"root": ["./src/main.ts", "./src/api/account.ts", "./src/api/assets.ts", "./src/api/branch.ts", "./src/api/config.ts", "./src/api/match.ts", "./src/api/order.ts", "./src/api/report.ts", "./src/api/typings.d.ts", "./src/api/user.ts", "./src/constants/color.ts", "./src/constants/index.ts", "./src/extension/enum.ts", "./src/extension/moment.ts", "./src/extension/numeral.ts", "./src/hooks/useprinter.ts", "./src/hooks/userequest.ts", "./src/hooks/useresettablereactive.ts", "./src/hooks/usescanlistener.ts", "./src/hooks/usethermalprinter.ts", "./src/i18n/index.ts", "./src/router/index.ts", "./src/router/routes.ts", "./src/store/global.ts", "./src/store/index.ts", "./src/typings/enum.ts", "./src/typings/typings.ts", "./src/utils/dialog.ts", "./src/utils/http.ts", "./src/utils/index.ts", "./src/utils/option.ts", "./src/views/spin/option.ts", "./src/app.vue", "./src/components/svgicon.vue", "./src/components/io/io-table/io-table.vue", "./src/components/io/io-upload/io-upload.vue", "./src/views/account/account.vue", "./src/views/account/modules/create-module.vue", "./src/views/account/modules/detail-module.vue", "./src/views/assets/assets.vue", "./src/views/assets/modules/transfer-module.vue", "./src/views/bet/bet.vue", "./src/views/branch/branch.vue", "./src/views/branch/modules/create-module.vue", "./src/views/branch/modules/detail-module.vue", "./src/views/components/navbar.vue", "./src/views/components/ticket.vue", "./src/views/dashboard/dashboard.vue", "./src/views/history/history.vue", "./src/views/history/orderdialog.vue", "./src/views/layout/layout.vue", "./src/views/match/match.vue", "./src/views/match/modules/config-module.vue", "./src/views/match/modules/detail-module.vue", "./src/views/passport/login.vue", "./src/views/printer/printer-dialog.vue", "./src/views/printer/printer.vue", "./src/views/report/report.vue", "./src/views/result/result.vue", "./src/views/setting/setting.vue", "./src/views/spin/spin.vue", "./src/views/ticket/ticket-dialog.vue", "./src/views/ticket/ticket.vue", "./typings/auto-import.d.ts", "./typings/components.d.ts", "./typings/global.d.ts", "./typings/vite-env.d.ts"], "errors": true, "version": "5.6.3"}