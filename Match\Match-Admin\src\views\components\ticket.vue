<template>
    <div class="panel ticket-panel">
        <div class="panel-header">
            <h3 class="panel-title">Ticket</h3>
        </div>
        <div class="panel-body">
            <table class="ticket-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">SN</th>
                        <th style="width: 80px;text-align: right;">Stake</th>
                        <th style="width: 60px;text-align: right;">Odds</th>
                        <th style="width: 80px;">Win</th>
                        <th style="width: 40px;"></th>
                    </tr>
                </thead>
                <el-scrollbar height="200px">
                    <tbody>
                        <tr v-for="(item, idx) in selections">
                            <td style="width: 50px;">{{ idx + 1 }}</td>
                            <td style="width: 80px;text-align: right;">{{ $numeral(item.stake) }}</td>
                            <td style="width: 60px;text-align: right;">{{ item.odds }}</td>
                            <td style="width: 80px;">{{ item.optionName }}</td>
                            <td style="width: 40px;">
                                <SvgIcon name="close" className="icon-close" @click="deleteSelection(item, idx)">
                                </SvgIcon>
                            </td>
                        </tr>
                    </tbody>
                </el-scrollbar>
                <tfoot>
                    <tr>
                        <td>Stake</td>
                        <td class="text-right text-bold" colspan="4">{{ $numeral(stake) }}</td>
                    </tr>
                    <tr>
                        <td>Max Payout</td>
                        <td class="text-right text-bold" colspan="4">{{ $numeral(maxPayout) }}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        <div class="panel-footer">
            <div class="stake">
                <div class="item" @click="onStakeClick(item)" v-for="item in allowStakes">{{ $numeral(item) }}</div>

                <div class="item custom">___</div>
            </div>
        </div>
    </div>

    <div class="panel">
        <div class="panel-body justify-between">
            <el-button style="width: 120px;" plain type="danger" @click="clearSelections">Clear
                Ticket</el-button>
            <el-button style="width: 120px;" type="primary" @click="submit" :disabled="selections.length == 0">Print
                Ticket</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import OrderService from '@/api/order';
import usePrinter from '@/hooks/usePrinter';

import { useGlobalStore } from '@/store/global';
import Dialog from '@/utils/dialog';


const allowStakes = [500, 1000, 2000, 5000, 10000, 20000, 50000];


const props = defineProps<{
    matchId: number,
    selections: Array<{
        stake: number;
        payout: number;
        odds: number;
        optionId: number;
        optionName: string;
        type: string;
    }>,
    selectedOption: {
        stake: number;
        payout: number;
        odds: number;
        optionId: number;
        optionName: string;
        type: string;
    }
}>();



const deleteSelection = (item: any, idx: number) => {
    props.selections.splice(idx, 1);
}

const clearSelections = () => {
    props.selections.length = 0;
}

const submit = () => {
    if (props.selections.length == 0) {
        return;
    }

    const globalStore = useGlobalStore();

    if (!globalStore.printerModel?.id) {
        return Dialog.warning('Please select a printer first.');
    }

    const formData = {
        matchId: props.matchId,
        round: 10000,
        items: props.selections
    };

    OrderService.submit(formData).then(res => {
        ElMessage({
            message: 'Order confirmed, please wait for printing.',
            type: 'success',
            plain: true,
        })
        clearSelections();
        usePrinter(res.result);
    });
}




const onStakeClick = (value: number) => {
    if (props.selectedOption.optionId == 0) {
        return;
    }
    props.selectedOption.stake = value;

    let item = props.selections.find(x => x.optionId == props.selectedOption.optionId);

    if (item != null) {
        item.stake += value;
        item.payout = item.stake * item.odds;
    } else {
        props.selections.push({
            stake: value,
            optionId: props.selectedOption.optionId,
            optionName: props.selectedOption.optionName,
            odds: props.selectedOption.odds,
            type: props.selectedOption.type,
            payout: value * props.selectedOption.odds,
        });
    }
}

const stake = computed(() => {
    return props.selections.reduce((acc, cur) => acc + cur.stake, 0);
})
const maxPayout = computed(() => {
    return props.selections.reduce((acc, cur) => acc + cur.payout, 0);
})

</script>

<style scoped lang="scss">
.ticket-panel {
    width: 340px;
}

.stake {
    display: flex;
    justify-content: flex-start;
    gap: 10px 20px;
    flex-wrap: wrap;
    .item {
        border-radius: 1000px;
        width: 60px;
        height: 60px;
        line-height: 60px;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 1px 1px rgba(54, 53, 53, 0.5);
        font-size: 16px;
        background-color: #dbd8d8;
    }
}

.ticket-table {
    width: 320px;
    border-radius: 3px;
    border-spacing: 0;
    white-space: nowrap;
    display: block;
    color: #3b3c3d;
    overflow: hidden;
    border: 1px solid #f5f5f5;

    tr {
        height: 20px;
    }

    td,
    th {
        padding: 3px;
        box-sizing: border-box;
    }

    thead {
        height: 20px;
        background-color: #dbd8d8;

        th {
            font-weight: bold;
            text-align: center;

        }
    }

    thead,
    tbody tr,
    tfoot tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        text-align: left;
    }

    tbody {
        display: block;

        tr {
            border-bottom: 1px solid #f5f4f1;

            td {
                text-align: center;

            }

            .type {
                display: block;
                font-size: 14px;
                color: #999;
                text-transform: uppercase;
            }
        }
    }

    tfoot {
        background-color: #dbd8d8;
    }
}
</style>