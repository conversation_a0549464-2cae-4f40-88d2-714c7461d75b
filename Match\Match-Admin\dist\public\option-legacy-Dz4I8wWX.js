System.register([],(function(l,e){"use strict";return{execute:function(){function e(l,e={withAll:!1,withEmpty:!1,value:"",prefix:""}){let t=[];return null!=e&&e.prefix&&(null==e?void 0:e.prefix.length)>0&&(e.prefix=`${e.prefix}.`),Object.keys(l).forEach((u=>{isNaN(Number(u))&&t.push({label:`${e.prefix}${u}`,value:l[u]})})),null!=e&&e.withAll&&t.unshift({label:"All",value:null==e?void 0:e.value}),null!=e&&e.withEmpty&&t.unshift({label:"",value:null==e?void 0:e.value}),t}l({a:e,g:function(l,t="",u=""){return e(l,{withEmpty:!1,withAll:!0,value:t,prefix:u})}})}}}));
