import{d as m,A as d,o as i,l as f,a as _,w as C,$ as z,a0 as h,u as b,T as y,c as v,R as S,F as k,a1 as x}from"./.pnpm-DuVJJfpW.js";import{_ as I}from"./_plugin-vue_export-helper-DlAUqK2U.js";const P=m({__name:"io-table",props:{layout:{default:"total ,prev, pager, next, sizes, jumper"},pagination:{default:()=>({pageIndex:1,pageSize:10,totalCount:0,pageSizes:[10,20,50,100]})}},emits:["pager-change"],setup(r,{expose:p,emit:l}){const t=r,g=l,u=a=>{s(t.pagination.pageIndex,a)},c=a=>{s(a,t.pagination.pageSize)},s=(a,e)=>{t.pagination.pageIndex=a,t.pagination.pageSize=e,g("pager-change",{pageIndex:a,pageSize:e})},o=d("table");return p(new Proxy({},{get(a,e){var n;return(n=o.value)==null?void 0:n[e]},has(a,e){return o.value?e in o.value:!1}})),(a,e)=>{const n=x;return i(),f(k,null,[_(b(y),h({"table-layout":"fixed",fit:"",stripe:"",ref:"table"},a.$attrs),{default:C(()=>[z(a.$slots,"default",{},void 0,!0)]),_:3},16),a.pagination.totalCount?(i(),v(n,{key:0,class:"pagination",background:"",layout:t.layout,"current-page":a.pagination.pageIndex,"page-sizes":[1,2,50,100],"page-size":a.pagination.pageSize,total:a.pagination.totalCount,onSizeChange:u,onCurrentChange:c},null,8,["layout","current-page","page-size","total"])):S("",!0)],64)}}}),E=I(P,[["__scopeId","data-v-8aac2784"]]);export{E as _};
