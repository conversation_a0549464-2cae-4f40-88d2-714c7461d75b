System.register(["./http-legacy-BkRyiHlu.js"],(function(t,e){"use strict";var a;return{setters:[t=>{a=t.h}],execute:function(){t("A",class{static getPaginatedList(t){return a.post("/api/account/fetch",t)}static update(t){return a.post("/api/account/update",t)}static create(t){return a.post("/api/account/create",t)}static delete(t){return a.post("/api/account/delete",t)}}),t("B",class{static getPaginatedList(t){return a.post("/api/branch/fetch",t)}static update(t){return a.post("/api/branch/update",t)}static create(t){return a.post("/api/branch/create",t)}})}}}));
