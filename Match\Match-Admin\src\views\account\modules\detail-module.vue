<template>
    <el-dialog v-model="visible" title="账号信息" width="600" draggable :close-on-click-modal="false"
        :close-on-press-escape="false" @closed="reset">

        <el-form label-width="100px" :model="formData" ref="form" :show-message="false">
            <el-form-item label="账号类型" prop="accountType" required>
                <el-select v-model="formData.accountType" placeholder="" clearable>
                    <el-option v-for="item in EAccountTypes" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="所属店铺" prop="branchId" required v-if="formData.accountType == 30">
                <el-select v-model="formData.branchId" placeholder="" clearable>
                    <el-option v-for="item in branches" :label="item.branchName" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="用户名" prop="loginAccount" required>
                <el-input placeholder="" v-model="formData.loginAccount" readonly/>
            </el-form-item>
            <el-form-item label="昵称" prop="nickName" required>
                <el-input placeholder="" v-model="formData.nickName" />
            </el-form-item>
            <el-form-item label="状态">
                <el-radio-group v-model="formData.status">
                    <el-radio :value="10">启用</el-radio>
                    <el-radio :value="20">禁用</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="justify-center">
                <el-button type="danger" :disabled="loading" plain @click="close">取消</el-button>
                <el-button type="primary" :disabled="loading" @click="save">保存</el-button>
            </div>
        </template>
    </el-dialog>
</template>


<script lang="ts" setup>
import AccountService from '@/api/account';
import BranchService from '@/api/branch';
import { AccountModel } from '@/api/typings';
import { EAccountType, ENormalStatus } from '@/typings/enum';

import { getOptions } from '@/utils/option';


const EAccountTypes = getOptions(EAccountType);

const loading = ref(false);
const visible = ref(false);


const emits = defineEmits(['saved']);

const formData = ref<AccountModel>({
    status: ENormalStatus.Normal
} as AccountModel);

const form = useTemplateRef('form');
const save = () => {
    //表单验证
    form.value?.validate((valid, _fields) => {
        if (!valid) {
            return;
        }
        loading.value = true;
        AccountService.update(formData.value).then(res => {
            visible.value = false;
            emits('saved', formData.value);
        }).finally(() => {
            loading.value = false;
        });
    })
}
const reset = () => {
    form.value?.resetFields();
}


const open = (row: AccountModel) => {
    visible.value = true;
    formData.value = row;
}

const close = () => {
    visible.value = false;
}

const branches = ref<Array<any>>([]);
const getBranches = () => {
    BranchService.getPaginatedList({ pageIndex: 1, pageSize: 9999 }).then(res => {
        branches.value = res.items;
    });
}

onMounted(() => {
    getBranches();
})


defineExpose({
    open: open
});

</script>