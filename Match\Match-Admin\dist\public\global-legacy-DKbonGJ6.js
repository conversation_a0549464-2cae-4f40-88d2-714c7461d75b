!function(){function e(e,t,r,o,n,i,s){try{var a=e[i](s),u=a.value}catch(e){return void r(e)}a.done?t(u):Promise.resolve(u).then(o,n)}System.register(["./http-legacy-BkRyiHlu.js","./index-legacy-C9rPDH0t.js","./.pnpm-legacy-DHcv0MF7.js"],(function(t,r){"use strict";var o,n,i,s,a,u;return{setters:[e=>{o=e.h},e=>{n=e.T,i=e.r},e=>{s=e.a3,a=e.z,u=e.f}],execute:function(){class r{static login(e){return o.post("api/passport/login",e)}static getProfile(){return o.get("api/passport/profile")}static getPaginatedList(e){return o.post("/order/fetch",e)}}t("u",s("globalStore",(()=>{const t=a(),o=a(),s=a(),l=function(){var s,a=(s=function*(e){const{result:s}=yield r.login(e);t.value={token:s.token,expireAt:s.expireAt},o.value=s,u.set(n,s.token,{expires:new Date(s.expireAt)}),i.push("/")},function(){var t=this,r=arguments;return new Promise((function(o,n){var i=s.apply(t,r);function a(t){e(i,o,n,a,u,"next",t)}function u(t){e(i,o,n,a,u,"throw",t)}a(void 0)}))});return function(e){return a.apply(this,arguments)}}();return{tokenModel:t,profileModel:o,userLogout:()=>{t.value=void 0,o.value=void 0,u.remove(n),i.replace("/login")},userLogin:l,printerModel:s}}),{persist:{key:"globalStore",storage:window.localStorage}}))}}}))}();
