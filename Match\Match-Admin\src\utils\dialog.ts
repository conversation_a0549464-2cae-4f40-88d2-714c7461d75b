import { ElLoading } from "element-plus";

class Dialog {
    private static loading: any = null;
    public static showLoading() {
        this.loading = ElLoading.service({
            fullscreen: true,
            body: true,
            lock: true,
            text: '',
            // spinner: 'el-icon-loading',
            // spinner: 'fa fa-spinner fa-spin',
            background: 'rgba(0, 0, 0, 0.12)'
        });
    }
    public static closeLoading() {
        if (this.loading != null) {
            nextTick(() => {
                this.loading.close();
            });
        }
    }

    public static warning(message: string) {
        return ElMessageBox.alert(message, 'Warning', {
            type: 'warning',
            center: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonText: 'OK'
        })

    }
    public static confirm(message: string) {
        return ElMessageBox.confirm(message, 'Confirm', {
            type: 'warning',
            confirmButtonText: 'Confirm'
        })
    }

}

export default Dialog;