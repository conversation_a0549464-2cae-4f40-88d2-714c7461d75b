import{h as r}from"./http-QDC4lyIP.js";import{T as a,r as i}from"./index-Cc7WJN9q.js";import{a3 as u,z as s,f as n}from"./.pnpm-DuVJJfpW.js";class c{static login(e){return r.post("api/passport/login",e)}static getProfile(){return r.get("api/passport/profile")}static getPaginatedList(e){return r.post("/order/fetch",e)}}const k=u("globalStore",()=>{const t=s(),e=s(),l=s();return{tokenModel:t,profileModel:e,userLogout:()=>{t.value=void 0,e.value=void 0,n.remove(a),i.replace("/login")},userLogin:async p=>{const{result:o}=await c.login(p);t.value={token:o.token,expireAt:o.expireAt},e.value=o,n.set(a,o.token,{expires:new Date(o.expireAt)}),i.push("/")},printerModel:l}},{persist:{key:"globalStore",storage:window.localStorage}});export{k as u};
