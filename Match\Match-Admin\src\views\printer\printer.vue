<template>
    <div class="page-body">
        <div class="panel printer">
            <div class="panel-header with-border">
                <div class="panel-title">Printer
                    <span v-if="printerModel">(already set)</span>
                    <span v-else>(not set)</span>
                </div>
            </div>
            <div class="panel-body">
                <div class="mode"><span>Mode:</span> {{ printerModel?.type == 'tcp' ? 'Network' : printerModel?.type }}
                </div>
                <h3 class="name">{{ printerModel?.name }}</h3>
                <div class="address">{{ printerModel?.id }}</div>
                <!-- <div class="address"><span>Pager Width:</span> {{ printerModel?.paperWidth }}</div> -->
            </div>
            <div class="panel-footer justify-end">
                <el-button type="warning" @click="visible = true">Change</el-button>
            </div>
        </div>
    </div>
    <printer-dialog v-model="visible" />
</template>

<script lang="ts" setup>
import printerDialog from './printer-dialog.vue';
import { useGlobalStore } from '@/store/global';
import { storeToRefs } from 'pinia';

const globalStore = useGlobalStore();
const { printerModel } = storeToRefs(globalStore);

// 修改: 将 visible 的初始值设置为 false
const visible = ref(false);

</script>

<style lang="scss" scoped>
.printer {
    width: 350px;

    .name {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .address {
        color: #868788;
        margin-bottom: 5px;
    }

    .mode {
        font-size: 16px;
    }
}
</style>