import{d as S,z as v,o as V,c as C,w as s,p as R,a as l,v as f,u as a,D as I,J as K,H as z,I as F,V as A,W as G,L as J,x as w,M as O,Y as T,_ as H,O as Q,A as B,B as W,l as L,P as Y,F as Z,Q as X,t as M,R as $,S as h,Z as ee}from"./.pnpm-DuVJJfpW.js";import{_ as le}from"./io-table-DC_2HcLS.js";import{h as E}from"./http-QDC4lyIP.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-Cc7WJN9q.js";class P{static getMatches(m){return E.get("/api/match/list",{params:m})}static update(m){return E.post("/api/match/update",m)}}const ae={class:"justify-center"},oe=S({__name:"detail-module",emits:["saved"],setup(U,{expose:m,emit:x}){const g=v(!1),i=v(!1),t=x,d=v({}),k=()=>{P.update(d.value).then(u=>{i.value=!1,t("saved",d.value)})},b=u=>{i.value=!0,d.value=u},c=()=>{i.value=!1};return m({open:b}),(u,e)=>{const o=K,r=z,y=F,_=A,p=G,j=J,N=w,D=O;return V(),C(D,{modelValue:a(i),"onUpdate:modelValue":e[5]||(e[5]=n=>I(i)?i.value=n:null),title:"游戏信息",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:s(()=>[R("div",ae,[l(N,{type:"danger",disabled:a(g),plain:"",onClick:c},{default:s(()=>e[8]||(e[8]=[f("取消")])),_:1},8,["disabled"]),l(N,{type:"primary",disabled:a(g),onClick:k},{default:s(()=>e[9]||(e[9]=[f("保存")])),_:1},8,["disabled"])])]),default:s(()=>[l(j,{"label-width":"100px"},{default:s(()=>[l(r,{label:"游戏名",required:""},{default:s(()=>[l(o,{placeholder:"",modelValue:a(d).matchName,"onUpdate:modelValue":e[0]||(e[0]=n=>a(d).matchName=n)},null,8,["modelValue"])]),_:1}),l(r,{label:"时间间隔",required:""},{default:s(()=>[l(y,{placeholder:"",min:60,max:1800,modelValue:a(d).interval,"onUpdate:modelValue":e[1]||(e[1]=n=>a(d).interval=n),step:10,precision:0},null,8,["modelValue"])]),_:1}),l(r,{label:"开启时间",required:""},{default:s(()=>[l(o,{placeholder:"",modelValue:a(d).startTime,"onUpdate:modelValue":e[2]||(e[2]=n=>a(d).startTime=n)},null,8,["modelValue"])]),_:1}),l(r,{label:"结束时间",required:""},{default:s(()=>[l(o,{placeholder:"",modelValue:a(d).endTime,"onUpdate:modelValue":e[3]||(e[3]=n=>a(d).endTime=n)},null,8,["modelValue"])]),_:1}),l(r,{label:"状态",required:""},{default:s(()=>[l(p,{modelValue:a(d).status,"onUpdate:modelValue":e[4]||(e[4]=n=>a(d).status=n)},{default:s(()=>[l(_,{value:1},{default:s(()=>e[6]||(e[6]=[f("启用下单")])),_:1}),l(_,{value:0},{default:s(()=>e[7]||(e[7]=[f("禁用下单")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});class q{static update(m){return E.post("/api/config/update",m)}static get(m){return E.post("/api/config/get",m)}}const te={class:"justify-center"},ne=S({__name:"config-module",emits:["saved"],setup(U,{expose:m,emit:x}){const g=v(!1),i=v(!1),t=v({}),d=x,k=e=>{const o={configKey:"",matchName:e.matchName};switch(e.id){case 1e3:o.configKey="LuckyRouletten_Rule";break;case 2e3:o.configKey="CarsRacing_Rule";break;case 3e3:o.configKey="ColorLucky_Rule";break}t.value.matchName=e.matchName,q.get(o).then(r=>{t.value=r.result,t.value.jsonValue=JSON.parse(r.result.configValue),t.value.jsonValue.reserveRatio&&(t.value.jsonValue.reserveRatio=t.value.jsonValue.reserveRatio*100),t.value.matchName=e.matchName,i.value=!0})},b=e=>{k(e)},c=()=>{const e=T.cloneDeep(t.value);e.jsonValue.reserveRatio&&(e.jsonValue.reserveRatio=e.jsonValue.reserveRatio/100),e.configValue=JSON.stringify(e.jsonValue),q.update(e).then(o=>{i.value=!1,d("saved",e)})},u=()=>{i.value=!1};return m({open:b}),(e,o)=>{const r=K,y=z,_=H,p=F,j=J,N=w,D=O;return V(),C(D,{modelValue:a(i),"onUpdate:modelValue":o[9]||(o[9]=n=>I(i)?i.value=n:null),title:"游戏配置",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:s(()=>[R("div",te,[l(N,{type:"danger",disabled:a(g),plain:"",onClick:u},{default:s(()=>o[10]||(o[10]=[f("取消")])),_:1},8,["disabled"]),l(N,{type:"primary",disabled:a(g),onClick:c},{default:s(()=>o[11]||(o[11]=[f("保存")])),_:1},8,["disabled"])])]),default:s(()=>[l(j,{"label-width":"100px"},{default:s(()=>[l(y,{label:"游戏名"},{default:s(()=>[l(r,{placeholder:"",readonly:"",modelValue:a(t).matchName,"onUpdate:modelValue":o[0]||(o[0]=n=>a(t).matchName=n)},null,8,["modelValue"])]),_:1}),a(t).configKey=="ColorLucky_Rule"?(V(),C(y,{key:0,label:"开奖配置"},{default:s(()=>[l(_,{label:"8中8",modelValue:a(t).jsonValue["8_8"],"onUpdate:modelValue":o[1]||(o[1]=n=>a(t).jsonValue["8_8"]=n)},null,8,["modelValue"]),l(_,{label:"8中7",modelValue:a(t).jsonValue["8_7"],"onUpdate:modelValue":o[2]||(o[2]=n=>a(t).jsonValue["8_7"]=n)},null,8,["modelValue"]),l(_,{label:"7中7",modelValue:a(t).jsonValue["7_7"],"onUpdate:modelValue":o[3]||(o[3]=n=>a(t).jsonValue["7_7"]=n)},null,8,["modelValue"]),l(_,{label:"7中6",modelValue:a(t).jsonValue["7_6"],"onUpdate:modelValue":o[4]||(o[4]=n=>a(t).jsonValue["7_6"]=n)},null,8,["modelValue"]),l(_,{label:"6中6",modelValue:a(t).jsonValue["6_6"],"onUpdate:modelValue":o[5]||(o[5]=n=>a(t).jsonValue["6_6"]=n)},null,8,["modelValue"]),l(_,{label:"6中5",modelValue:a(t).jsonValue["6_5"],"onUpdate:modelValue":o[6]||(o[6]=n=>a(t).jsonValue["6_5"]=n)},null,8,["modelValue"]),l(_,{label:"5中5",modelValue:a(t).jsonValue["5_5"],"onUpdate:modelValue":o[7]||(o[7]=n=>a(t).jsonValue["5_5"]=n)},null,8,["modelValue"])]),_:1})):(V(),C(y,{key:1,label:"吃分比例(%)"},{default:s(()=>[l(p,{min:-20,max:50,step:1,style:{width:"100%"},modelValue:a(t).jsonValue.reserveRatio,"onUpdate:modelValue":o[8]||(o[8]=n=>a(t).jsonValue.reserveRatio=n)},null,8,["modelValue"])]),_:1}))]),_:1})]),_:1},8,["modelValue"])}}}),se={class:"page-body flex-col"},ue={class:"panel"},de={class:"panel-body",style:{padding:"0"}},fe=S({__name:"match",setup(U){const m=Q({pageIndex:1,pageSize:15,totalCount:0,totalPages:0}),x=B("DetailModuleRef"),g=c=>{var u;(u=x.value)==null||u.open(T.cloneDeep(c))},i=B("ConfigModelRef"),t=c=>{var u;(u=i.value)==null||u.open(T.cloneDeep(c))},d=v(!1),k=v([]),b=()=>{d.value=!0,P.getMatches({}).then(c=>{k.value=c.result}).finally(()=>{d.value=!1})};return W(()=>{b()}),(c,u)=>{const e=h,o=w,r=ee,y=le,_=X;return V(),L(Z,null,[R("div",se,[R("div",ue,[u[4]||(u[4]=R("div",{class:"panel-header with-border"},[R("div",{class:"panel-title"},"游戏列表")],-1)),Y((V(),L("div",de,[l(y,{height:"500",data:a(k),pagination:a(m)},{default:s(()=>[l(e,{prop:"id",label:"序号"},{default:s(({row:p,$index:j})=>[f(M(j+1),1)]),_:1}),l(e,{prop:"",label:"",fixed:"right"},{default:s(({row:p})=>[l(o,{plain:"",type:"primary",size:"small",onClick:j=>g(p)},{default:s(()=>u[0]||(u[0]=[f("编辑")])),_:2},1032,["onClick"]),l(o,{plain:"",type:"primary",size:"small",onClick:j=>t(p)},{default:s(()=>u[1]||(u[1]=[f("配置")])),_:2},1032,["onClick"])]),_:1}),l(e,{prop:"matchName",label:"游戏名"}),l(e,{prop:"interval",label:"时间间隔(s)"}),l(e,{prop:"startTime",label:"开启时间"},{default:s(({row:p})=>[f(M(p.startTime)+" - "+M(p.endTime),1)]),_:1}),l(e,{prop:"status",label:"状态"},{default:s(({row:p})=>[p.status==0?(V(),C(r,{key:0,type:"danger"},{default:s(()=>u[2]||(u[2]=[f("禁用")])),_:1})):$("",!0),p.status==1?(V(),C(r,{key:1,type:"success"},{default:s(()=>u[3]||(u[3]=[f("启用")])),_:1})):$("",!0)]),_:1})]),_:1},8,["data","pagination"])])),[[_,a(d)]])])]),l(oe,{ref:"DetailModuleRef",onSaved:b},null,512),l(ne,{ref:"ConfigModelRef",onSaved:b},null,512)],64)}}});export{fe as default};
