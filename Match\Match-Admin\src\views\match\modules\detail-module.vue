<template>
    <el-dialog v-model="visible" title="游戏信息" width="600" draggable :close-on-click-modal="false"
        :close-on-press-escape="false">

        <el-form label-width="100px">
            <el-form-item label="游戏名" required>
                <el-input placeholder="" v-model="formData.matchName" />
            </el-form-item>
            <el-form-item label="时间间隔" required>
                <el-input-number placeholder="" :min="60" :max="1800" v-model="formData.interval" :step="10"
                    :precision="0" />
            </el-form-item>
            <el-form-item label="开启时间" required>
                <el-input placeholder="" v-model="formData.startTime" />
                <!-- <el-time-select v-model="formData.startTime" format="HH:mm:ss" /> -->
            </el-form-item>
            <el-form-item label="结束时间" required>
                <el-input placeholder="" v-model="formData.endTime" />
                <!-- <el-time-select v-model="formData.endTime" format="HH:mm:ss" /> -->
            </el-form-item>
            <el-form-item label="状态" required>
                <el-radio-group v-model="formData.status">
                    <el-radio :value="1">启用下单</el-radio>
                    <el-radio :value="0">禁用下单</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="justify-center">
                <el-button type="danger" :disabled="loading" plain @click="close">取消</el-button>
                <el-button type="primary" :disabled="loading" @click="save">保存</el-button>
            </div>
        </template>
    </el-dialog>
</template>


<script lang="ts" setup>
import MatchService from '@/api/match';
import { MatchModel } from '@/api/typings';


const loading = ref(false);
const visible = ref(false);


const emits = defineEmits(['saved']);

const formData = ref<MatchModel>({} as MatchModel);

const save = () => {
    MatchService.update(formData.value).then(res => {
        visible.value = false;
        emits('saved', formData.value);
    });
}


const open = (row: MatchModel) => {
    visible.value = true;
    formData.value = row;
}

const close = () => {
    visible.value = false;
}



defineExpose({
    open: open
});

</script>