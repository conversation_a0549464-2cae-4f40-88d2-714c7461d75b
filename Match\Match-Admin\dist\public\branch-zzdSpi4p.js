import{h as a}from"./http-QDC4lyIP.js";class r{static getPaginatedList(t){return a.post("/api/account/fetch",t)}static update(t){return a.post("/api/account/update",t)}static create(t){return a.post("/api/account/create",t)}static delete(t){return a.post("/api/account/delete",t)}}class s{static getPaginatedList(t){return a.post("/api/branch/fetch",t)}static update(t){return a.post("/api/branch/update",t)}static create(t){return a.post("/api/branch/create",t)}}export{r as A,s as B};
