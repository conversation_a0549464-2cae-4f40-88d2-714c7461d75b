import { reactive } from "vue";


function defaultClone(value: any) {
    if (value === null || typeof value !== 'object') return value;
    return JSON.parse(JSON.stringify(value));
}


export function useResettableReactive<T extends object>(value?: T, clone = defaultClone) {

    const state = reactive(clone(value)) as T;

    const reset = () => {
        Object.keys(state).forEach(key => delete state[key as keyof typeof state]);
        Object.assign(state, clone(value))
    }

    return [state, reset] as const
}