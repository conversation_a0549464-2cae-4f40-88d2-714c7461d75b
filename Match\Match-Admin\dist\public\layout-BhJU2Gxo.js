import{_ as N}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{k as u,o as r,l as d,m as C,p as e,q as b,d as x,t as v,u as f,v as g,a as t,w as l,x as h,r as k,c as y,y as $,K as S}from"./.pnpm-DuVJJfpW.js";import{u as p}from"./global-BZMqDQCr.js";import"./http-QDC4lyIP.js";import"./index-Cc7WJN9q.js";const w=["xlink:href"],B={__name:"SvgIcon",props:{name:{type:String,required:!0},className:{type:String,default:""}},setup(m){const n=m,s=u(()=>/^(https?:|mailto:|tel:)/.test(n.name)),a=u(()=>"#icon-".concat(n.name)),o=u(()=>n.className?"svg-icon "+n.className:"svg-icon"),i=u(()=>({mask:"url(".concat(n.name,") no-repeat 50% 50%"),"-webkit-mask":"url(".concat(n.name,") no-repeat 50% 50%")}));return(c,_)=>s.value?(r(),d("div",{key:0,style:C(i.value),class:"svg-external-icon svg-icon"},null,4)):(r(),d("svg",{key:1,class:b(o.value),"aria-hidden":"true"},[e("use",{"xlink:href":a.value},null,8,w)],2))}},I=N(B,[["__scopeId","data-v-e42162f1"]]),E={class:"nav-header flex-between"},V=x({__name:"navbar",setup(m){const{profileModel:n}=p(),s=()=>{p().userLogout()};return(a,o)=>{var c,_;const i=h;return r(),d("div",E,[e("div",null,[e("span",null," Hello "+v((c=f(n))==null?void 0:c.nickName),1),o[0]||(o[0]=g()),e("b",null,"("+v((_=f(n))==null?void 0:_.branchName)+")",1)]),e("div",null,[t(i,{type:"danger",onClick:s},{default:l(()=>o[1]||(o[1]=[g("Logout")])),_:1})])])}}}),q={class:"full-page console-page"},z={class:"console-nav"},D={class:"menu"},K={class:"item"},L={class:"item"},A={class:"item"},G={class:"item"},H={class:"item"},M={class:"item"},P={class:"console-body"},T=x({__name:"layout",setup(m){return p(),(n,s)=>{const a=I,o=k("router-link"),i=k("router-view");return r(),d("div",q,[e("div",z,[s[6]||(s[6]=e("div",{class:"logo"},null,-1)),e("div",D,[e("div",K,[t(o,{to:"/dashboard",activeClass:"active"},{default:l(()=>[t(a,{name:"home",className:"icon-home"}),s[0]||(s[0]=e("span",null,"工作台",-1))]),_:1})]),e("div",L,[t(o,{to:"/branch",activeClass:"active"},{default:l(()=>[t(a,{name:"order",className:"icon-order"}),s[1]||(s[1]=e("span",null,"店铺管理",-1))]),_:1})]),e("div",A,[t(o,{to:"/report",activeClass:"active"},{default:l(()=>[t(a,{name:"order",className:"icon-order"}),s[2]||(s[2]=e("span",null,"报表中心",-1))]),_:1})]),e("div",G,[t(o,{to:"/assets",activeClass:"active"},{default:l(()=>[t(a,{name:"order",className:"icon-order"}),s[3]||(s[3]=e("span",null,"财务中心",-1))]),_:1})]),e("div",H,[t(o,{to:"/match",activeClass:"active"},{default:l(()=>[t(a,{name:"order",className:"icon-order"}),s[4]||(s[4]=e("span",null,"游戏管理",-1))]),_:1})]),e("div",M,[t(o,{to:"/account",activeClass:"active"},{default:l(()=>[t(a,{name:"order",className:"icon-order"}),s[5]||(s[5]=e("span",null,"账户中心",-1))]),_:1})])])]),e("div",P,[t(V),t(i,null,{default:l(({Component:c})=>[(r(),y(S,null,[(r(),y($(c),{key:n.$route.fullPath}))],1024))]),_:1})])])}}}),R=N(T,[["__scopeId","data-v-7de44e9f"]]);export{R as default};
