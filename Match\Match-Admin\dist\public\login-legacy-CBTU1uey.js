!function(){function e(e,n,l,t,a,o,i){try{var r=e[o](i),d=r.value}catch(e){return void l(e)}r.done?n(d):Promise.resolve(d).then(t,a)}function n(n){return function(){var l=this,t=arguments;return new Promise((function(a,o){var i=n.apply(l,t);function r(n){e(i,a,o,r,d,"next",n)}function d(n){e(i,a,o,r,d,"throw",n)}r(void 0)}))}}System.register(["./.pnpm-legacy-DHcv0MF7.js","./global-legacy-DKbonGJ6.js","./_plugin-vue_export-helper-legacy-DgAO6S8O.js","./http-legacy-BkRyiHlu.js","./index-legacy-C9rPDH0t.js"],(function(e,l){"use strict";var t,a,o,i,r,d,u,s,c,g,p,f,m,v,y,h,x,w,_;return{setters:[e=>{t=e.d,a=e.k,o=e.O,i=e.z,r=e.A,d=e.o,u=e.l,s=e.p,c=e.a,g=e.w,p=e.u,f=e.a2,m=e.L,v=e.v,y=e.J,h=e.H,x=e.x},e=>{w=e.u},e=>{_=e._},null,null],execute:function(){var l=document.createElement("style");l.textContent=".login-page[data-v-af24d1ee]{display:flex;align-items:center;justify-content:center}.login-body[data-v-af24d1ee]{padding:40px;width:100%;max-width:410px}.login-title[data-v-af24d1ee]{margin-bottom:10px;text-align:center;font-size:40px;color:#2196f3;padding:15px 0;font-weight:700}.login-form[data-v-af24d1ee]{--el-button-size: 45px}.login-form .input[data-v-af24d1ee]{margin-bottom:20px;--el-component-size-large: 45px}\n/*$vite$:1*/",document.head.appendChild(l);const b={class:"full-page login-page"},z={class:"login-body"},P=t({__name:"login",setup(e){f();const l=a((()=>""===t.loginAccount||""===t.loginPassword)),t=o({loginAccount:"sysadmin",loginPassword:"********"}),_=i(!1),{userLogin:P}=w(),j=r("loginForm"),A=function(){var e=n((function*(){var e;null===(e=j.value)||void 0===e||e.validate(function(){var e=n((function*(e,n){if(e)try{_.value=!0,P(t)}finally{_.value=!1}}));return function(n,l){return e.apply(this,arguments)}}())}));return function(){return e.apply(this,arguments)}}();return(e,n)=>{const a=y,o=h,i=x,r=m;return d(),u("div",b,[s("div",z,[n[3]||(n[3]=s("div",{class:"login-title"}," Magic ",-1)),c(r,{model:p(t),"show-message":!1,class:"login-form",ref_key:"loginForm",ref:j},{default:g((()=>[c(o,{label:"",prop:"loginAccount",class:"input",required:""},{default:g((()=>[c(a,{placeholder:"username",modelValue:p(t).loginAccount,"onUpdate:modelValue":n[0]||(n[0]=e=>p(t).loginAccount=e),size:"large"},null,8,["modelValue"])])),_:1}),c(o,{label:"",prop:"loginPassword",class:"input",required:""},{default:g((()=>[c(a,{placeholder:"password",type:"password",modelValue:p(t).loginPassword,"onUpdate:modelValue":n[1]||(n[1]=e=>p(t).loginPassword=e),size:"large"},null,8,["modelValue"])])),_:1}),c(o,{class:"input"},{default:g((()=>[c(i,{type:"primary",disabled:p(l),loading:p(_),class:"full-width",size:"large",onClick:A},{default:g((()=>n[2]||(n[2]=[v(" Login ")]))),_:1},8,["disabled","loading"])])),_:1})])),_:1},8,["model"])])])}}});e("default",_(P,[["__scopeId","data-v-af24d1ee"]]))}}}))}();
