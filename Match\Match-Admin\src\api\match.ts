import http from '@/utils/http';
import { MatchModel, Result } from './typings';

export default class MatchService {
    public static getMatches(params: { matchName?: string; status?: number; }) {
        return http.get<any, Result<Array<MatchModel>>>("/api/match/list", { params });
    }

    public static update(data: MatchModel) {
        return http.post<any, Result<MatchModel>>("/api/match/update", data);
    }
}