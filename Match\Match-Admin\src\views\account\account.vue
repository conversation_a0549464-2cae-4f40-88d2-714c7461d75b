<template>
    <div class="page-body flex-col">
        <div class="panel">
            <div class="panel-header with-border">
                <div class="panel-title">搜索条件</div>
            </div>
            <div class="panel-body">
                <el-form :model="formData" :inline="true" ref="searchForm" class="search-form">
                    <el-form-item label="用户名" prop="loginAccount">
                        <el-input placeholder="" v-model.trim="formData.loginAccount" />
                    </el-form-item>
                    <el-form-item label="昵称" prop="nickName">
                        <el-input placeholder="" v-model.trim="formData.nickName" />
                    </el-form-item>
                    <el-form-item label="所属店铺" prop="branchName">
                        <el-input placeholder="" v-model.trim="formData.branchName" />
                    </el-form-item>
                    <el-form-item label="账号类型" prop="accountType">
                        <el-select v-model="formData.accountType" placeholder="" clearable>
                            <el-option v-for="item in EAccountTypes" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search">搜索</el-button>
                        <el-button type="danger" plain @click="reset">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="panel">
            <div class="panel-header with-border">
                <div class="panel-title">账号列表</div>
                <div class="panel-tools">
                    <el-button type="primary" @click="add">添加</el-button>
                </div>
            </div>
            <div class="panel-body" style="padding: 0;" v-loading="loading">
                <io-table height="400" :data="rows" :pagination="pagination" @pager-change="getPaginatedList">
                    <el-table-column prop="id" label="序号" width="55" align="center">
                        <template #default="{ row, $index }" >
                            {{ $index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="" label="" fixed="right">
                        <template #default="{ row }">
                            <el-button plain type="primary" size="small" @click="view(row)">编辑</el-button>
                            <!-- <el-button plain type="primary" size="small" disabled>重置密码</el-button> -->
                        </template>
                    </el-table-column>
                    <el-table-column prop="loginAccount" label="用户名" show-overflow-tooltip/>
                    <el-table-column prop="nickName" label="昵称" />
                    <el-table-column prop="branch" label="所属店铺">
                        <template #default="{ row }">
                            {{ row.branch?.branchName }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="accountType" label="账号类型">
                        <template #default="{ row }">
                            <span v-enum="{ type: EAccountType, value: row.accountType }"></span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="assets.balance" label="余额">
                        <template #default="{ row }">
                            {{ $numeral(row.assets?.balance) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态">
                        <template #default="{ row }">
                            <el-tag type="success" v-if="row.status == 10">启用</el-tag>
                            <el-tag type="danger" v-if="row.status == 20">禁用</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="添加时间" width="140">
                        <template #default="{ row }">
                            {{ $moment(row.createTime) }}
                        </template>
                    </el-table-column>
                </io-table>
            </div>
        </div>
    </div>
    <detail-module ref="DetailModuleRef" @saved="getPaginatedList"></detail-module>
    <create-module ref="CreateModuleRef" @saved="getPaginatedList"></create-module>
</template>

<script lang="ts" setup>
import AccountService from '@/api/account';
import { AccountModel } from '@/api/typings';
import { EAccountType } from '@/typings/enum';
import DetailModule from './modules/detail-module.vue';
import CreateModule from './modules/create-module.vue';
import { cloneDeep } from 'lodash';
import { getOptionsWithAll } from '@/utils/option';


const EAccountTypes = getOptionsWithAll(EAccountType);
const formData = reactive({
    loginAccount: '',
    nickName: '',
    branchName: '',
    accountType: ''
})


const pagination = reactive({
    pageIndex: 1,
    pageSize: 15,
    totalCount: 0,
    totalPages: 0,
});

const search = () => {
    pagination.pageIndex = 1;
    getPaginatedList();
}

const searchForm = useTemplateRef('searchForm');
const reset = () => {
    searchForm.value?.resetFields();
    search();
}

const detailModuleRef = useTemplateRef<InstanceType<typeof DetailModule>>('DetailModuleRef');
const view = (row: AccountModel) => {
    detailModuleRef.value?.open(cloneDeep(row));
}

const createModuleRef = useTemplateRef<InstanceType<typeof CreateModule>>('CreateModuleRef');
const add = () => {
    createModuleRef.value?.open();
}


const rows = ref<Array<AccountModel>>([]);
const loading = ref(false);
const getPaginatedList = () => {
    loading.value = true;
    AccountService.getPaginatedList({ ...formData, ...pagination }).then(res => {
        rows.value = res.items;
        pagination.pageIndex = res.pageIndex;
        pagination.totalCount = res.totalCount;
        pagination.totalPages = res.totalPages;
    }).finally(() => {
        loading.value = false;
    });
}

onMounted(() => {
    getPaginatedList();
})
</script>