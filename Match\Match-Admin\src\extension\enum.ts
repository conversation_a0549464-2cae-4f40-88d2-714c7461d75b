import type { App, Directive, DirectiveBinding } from 'vue'

import COLOR_LIST from '@/constants/color';
import i18n from '@/i18n/index';
import { EnumDescription } from '@/constants/color';

const key = 'enum';



const customDirective: Directive = {
    mounted(el: HTMLElement, binding: DirectiveBinding): void {
        handle(el, binding);
    },
    updated(el: HTMLElement, binding: DirectiveBinding): void {
        handle(el, binding);
    }
}

const handle = (el: HTMLElement, binding: DirectiveBinding) => {
    const { type, value, bold = true } = binding?.value;

    if (typeof (value) == 'undefined' || value == null)
        return

    let item = COLOR_LIST.find((e: EnumDescription) => e.type == type && e.value == value);
    if (item != null) {
        el.style.color = item.color;
        el.style.fontWeight = bold ? 'bold' : 'normal';
        el.innerText = i18n.global.t(item.text ?? type[value]);
    } else {
        el.innerText = '';
    }
}

export default function install(app: App) {
    app.directive(key, customDirective)
}