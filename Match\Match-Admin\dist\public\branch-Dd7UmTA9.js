import{d as T,z as V,A as S,B as A,o as y,c as U,w as t,p as b,a as e,v as p,u as o,l as P,C as J,F as B,D as Q,J as L,H as $,G as W,V as Y,W as Z,L as z,x as q,M as K,N as X,O as G,P as ae,Q as te,t as O,R as H,Y as oe,S as ne,Z as se}from"./.pnpm-DuVJJfpW.js";import{_ as de}from"./io-table-DC_2HcLS.js";import{B as h,A as ee}from"./branch-zzdSpi4p.js";import{E as le}from"./index-Cc7WJN9q.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./http-QDC4lyIP.js";const ue={class:"justify-center"},re=T({__name:"detail-module",emits:["saved"],setup(j,{expose:f,emit:g}){const m=V(!1),i=V(!1),w=g,n=V({status:10}),N=S("form"),I=()=>{var a;(a=N.value)==null||a.validate((l,c)=>{if(!l){m.value=!1;return}m.value=!0,h.update(n.value).then(r=>{i.value=!1,w("saved",n.value)}).finally(()=>{m.value=!1})})},M=()=>{var a;(a=N.value)==null||a.resetFields()},k=a=>{i.value=!0,n.value=a},x=()=>{i.value=!1},v=V([]),u=()=>{ee.getPaginatedList({pageIndex:1,pageSize:9999,accountType:le.Agent}).then(a=>{v.value=a.items})};return A(()=>{u()}),f({open:k}),(a,l)=>{const c=L,r=$,R=X,_=W,C=Y,D=Z,F=z,d=q,E=K;return y(),U(E,{modelValue:o(i),"onUpdate:modelValue":l[5]||(l[5]=s=>Q(i)?i.value=s:null),title:"店铺信息",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1,onClosed:M},{footer:t(()=>[b("div",ue,[e(d,{type:"danger",disabled:o(m),plain:"",onClick:x},{default:t(()=>l[8]||(l[8]=[p("取消")])),_:1},8,["disabled"]),e(d,{type:"primary",disabled:o(m),onClick:I},{default:t(()=>l[9]||(l[9]=[p("保存")])),_:1},8,["disabled"])])]),default:t(()=>[e(F,{"label-width":"100px",ref_key:"form",ref:N,model:o(n),"show-message":!1},{default:t(()=>[e(r,{label:"名称",required:"",prop:"branchName"},{default:t(()=>[e(c,{placeholder:"",modelValue:o(n).branchName,"onUpdate:modelValue":l[0]||(l[0]=s=>o(n).branchName=s)},null,8,["modelValue"])]),_:1}),e(r,{label:"联系人",prop:"contactName"},{default:t(()=>[e(c,{placeholder:"",modelValue:o(n).contactName,"onUpdate:modelValue":l[1]||(l[1]=s=>o(n).contactName=s)},null,8,["modelValue"])]),_:1}),e(r,{label:"地址",prop:"address"},{default:t(()=>[e(c,{placeholder:"",modelValue:o(n).address,"onUpdate:modelValue":l[2]||(l[2]=s=>o(n).address=s)},null,8,["modelValue"])]),_:1}),e(r,{label:"所属代理商",required:"",prop:"accountId"},{default:t(()=>[e(_,{placeholder:"",clearable:"",modelValue:o(n).accountId,"onUpdate:modelValue":l[3]||(l[3]=s=>o(n).accountId=s)},{default:t(()=>[(y(!0),P(B,null,J(o(v),s=>(y(),U(R,{label:s.nickName,value:s.id},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"状态"},{default:t(()=>[e(D,{modelValue:o(n).status,"onUpdate:modelValue":l[4]||(l[4]=s=>o(n).status=s)},{default:t(()=>[e(C,{value:10},{default:t(()=>l[6]||(l[6]=[p("启用")])),_:1}),e(C,{value:20},{default:t(()=>l[7]||(l[7]=[p("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}}),ie={class:"justify-center"},pe=T({__name:"create-module",emits:["saved"],setup(j,{expose:f,emit:g}){const m=V(!1),i=V(!1),w=g,n=V({status:10}),N=S("form"),I=()=>{var a;(a=N.value)==null||a.validate((l,c)=>{if(!l){m.value=!1;return}m.value=!0,h.create(n.value).then(r=>{i.value=!1,w("saved",n.value)}).finally(()=>{m.value=!1})})},M=()=>{var a;(a=N.value)==null||a.resetFields()},k=()=>{i.value=!0},x=()=>{i.value=!1},v=V([]),u=()=>{ee.getPaginatedList({pageIndex:1,pageSize:9999,accountType:le.Agent}).then(a=>{v.value=a.items})};return A(()=>{u()}),f({open:k}),(a,l)=>{const c=L,r=$,R=X,_=W,C=Y,D=Z,F=z,d=q,E=K;return y(),U(E,{modelValue:o(i),"onUpdate:modelValue":l[5]||(l[5]=s=>Q(i)?i.value=s:null),title:"店铺信息",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1,onClosed:M},{footer:t(()=>[b("div",ie,[e(d,{type:"danger",disabled:o(m),plain:"",onClick:x},{default:t(()=>l[8]||(l[8]=[p("取消")])),_:1},8,["disabled"]),e(d,{type:"primary",disabled:o(m),onClick:I},{default:t(()=>l[9]||(l[9]=[p("保存")])),_:1},8,["disabled"])])]),default:t(()=>[e(F,{"label-width":"100px",ref_key:"form",ref:N,model:o(n),"show-message":!1},{default:t(()=>[e(r,{label:"名称",required:"",prop:"branchName"},{default:t(()=>[e(c,{placeholder:"",modelValue:o(n).branchName,"onUpdate:modelValue":l[0]||(l[0]=s=>o(n).branchName=s)},null,8,["modelValue"])]),_:1}),e(r,{label:"联系人",prop:"contactName"},{default:t(()=>[e(c,{placeholder:"",modelValue:o(n).contactName,"onUpdate:modelValue":l[1]||(l[1]=s=>o(n).contactName=s)},null,8,["modelValue"])]),_:1}),e(r,{label:"地址",prop:"address"},{default:t(()=>[e(c,{placeholder:"",modelValue:o(n).address,"onUpdate:modelValue":l[2]||(l[2]=s=>o(n).address=s)},null,8,["modelValue"])]),_:1}),e(r,{label:"所属代理商",required:"",prop:"accountId"},{default:t(()=>[e(_,{placeholder:"",clearable:"",modelValue:o(n).accountId,"onUpdate:modelValue":l[3]||(l[3]=s=>o(n).accountId=s)},{default:t(()=>[(y(!0),P(B,null,J(o(v),s=>(y(),U(R,{label:s.nickName,value:s.id},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"状态"},{default:t(()=>[e(D,{modelValue:o(n).status,"onUpdate:modelValue":l[4]||(l[4]=s=>o(n).status=s)},{default:t(()=>[e(C,{value:10},{default:t(()=>l[6]||(l[6]=[p("启用")])),_:1}),e(C,{value:20},{default:t(()=>l[7]||(l[7]=[p("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}}),me={class:"page-body flex-col"},ce={class:"panel"},fe={class:"panel-body"},_e={class:"panel"},ve={class:"panel-header with-border"},be={class:"panel-tools"},ge={class:"panel-body",style:{padding:"0"}},Ee=T({__name:"branch",setup(j){const f=G({branchName:"",contactName:"",address:""}),g=G({pageIndex:1,pageSize:15,totalCount:0,totalPages:0}),m=()=>{g.pageIndex=1,v()},i=S("searchForm"),w=()=>{var u;(u=i.value)==null||u.resetFields(),m()},n=S("DetailModuleRef"),N=u=>{var a;(a=n.value)==null||a.open(oe.cloneDeep(u))},I=S("CreateModuleRef"),M=()=>{var u;(u=I.value)==null||u.open()},k=V(!1),x=V([]),v=()=>{k.value=!0,h.getPaginatedList({...f,...g}).then(u=>{x.value=u.items,g.pageIndex=u.pageIndex,g.totalCount=u.totalCount,g.totalPages=u.totalPages}).finally(()=>{k.value=!1})};return A(()=>{v()}),(u,a)=>{const l=L,c=$,r=q,R=z,_=ne,C=se,D=de,F=te;return y(),P(B,null,[b("div",me,[b("div",ce,[a[5]||(a[5]=b("div",{class:"panel-header with-border"},[b("div",{class:"panel-title"},"查询条件")],-1)),b("div",fe,[e(R,{inline:!0,ref_key:"searchForm",ref:i,model:o(f),class:"search-form"},{default:t(()=>[e(c,{label:"名称",prop:"branchName"},{default:t(()=>[e(l,{placeholder:"",clearable:"",modelValue:o(f).branchName,"onUpdate:modelValue":a[0]||(a[0]=d=>o(f).branchName=d),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(c,{label:"联系人",prop:"contactName"},{default:t(()=>[e(l,{placeholder:"",clearable:"",modelValue:o(f).contactName,"onUpdate:modelValue":a[1]||(a[1]=d=>o(f).contactName=d),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(c,{label:"地址",prop:"address"},{default:t(()=>[e(l,{placeholder:"",clearable:"",modelValue:o(f).address,"onUpdate:modelValue":a[2]||(a[2]=d=>o(f).address=d),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(c,null,{default:t(()=>[e(r,{type:"primary",onClick:m,disabled:o(k)},{default:t(()=>a[3]||(a[3]=[p("查询")])),_:1},8,["disabled"]),e(r,{type:"danger",plain:"",onClick:w,disabled:o(k)},{default:t(()=>a[4]||(a[4]=[p("重置")])),_:1},8,["disabled"])]),_:1})]),_:1},8,["model"])])]),b("div",_e,[b("div",ve,[a[7]||(a[7]=b("div",{class:"panel-title"},"店铺列表",-1)),b("div",be,[e(r,{type:"primary",onClick:M},{default:t(()=>a[6]||(a[6]=[p("添加")])),_:1})])]),ae((y(),P("div",ge,[e(D,{height:"500",data:o(x),pagination:o(g),onPagerChange:v},{default:t(()=>[e(_,{prop:"id",label:"序号"},{default:t(({row:d,$index:E})=>[p(O(E+1),1)]),_:1}),e(_,{prop:"",label:"",width:"100",fixed:"right"},{default:t(({row:d})=>[e(r,{plain:"",type:"primary",size:"small",onClick:E=>N(d)},{default:t(()=>a[8]||(a[8]=[p("编辑")])),_:2},1032,["onClick"])]),_:1}),e(_,{prop:"branchName",label:"店铺名称"}),e(_,{prop:"contactName",label:"联系人"}),e(_,{prop:"address",label:"地址"}),e(_,{prop:"account.nickName",label:"所属代理商"}),e(_,{prop:"status",label:"状态"},{default:t(({row:d})=>[d.status==20?(y(),U(C,{key:0,type:"danger"},{default:t(()=>a[9]||(a[9]=[p("禁用")])),_:1})):H("",!0),d.status==10?(y(),U(C,{key:1,type:"success"},{default:t(()=>a[10]||(a[10]=[p("启用")])),_:1})):H("",!0)]),_:1}),e(_,{prop:"createTime",label:"添加时间"},{default:t(({row:d})=>[p(O(u.$moment(d.createTime)),1)]),_:1})]),_:1},8,["data","pagination"])])),[[F,o(k)]])])]),e(re,{ref:"DetailModuleRef",onSaved:v},null,512),e(pe,{ref:"CreateModuleRef",onSaved:v},null,512)],64)}}});export{Ee as default};
