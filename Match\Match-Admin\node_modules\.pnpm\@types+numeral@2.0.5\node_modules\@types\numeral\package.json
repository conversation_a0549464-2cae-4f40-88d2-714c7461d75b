{"name": "@types/numeral", "version": "2.0.5", "description": "TypeScript definitions for numeral", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/numeral", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "vbortone", "url": "https://github.com/vbortone"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/klujanrosas"}, {"name": "<PERSON>", "githubUsername": "Karlos<PERSON>", "url": "https://github.com/KarlosQ"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/numeral"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "e6a9ea01b3b12429c27af36d31d0684eeb3b598dc2b1df8d4c80cfcd7cd3c2ee", "typeScriptVersion": "4.5"}