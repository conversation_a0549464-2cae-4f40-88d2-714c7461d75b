<template>
    <div class="page-body flex-col">
        <div class="panel">
            <div class="panel-header with-border">
                <div class="panel-title">报表查询</div>
            </div>
            <div class="panel-body">
                <el-form ref="searchForm" :model="formData" class="search-form" label-width="70px"
                    label-position="left">
                    <el-row :gutter="20">
                        <el-col :span="6" v-if="globalStore.profileModel?.accountType == 'Admin'">
                            <el-form-item label="代理" prop="agentId">
                                <el-select placeholder="" clearable v-model="formData.agentId" @change="onAgentChange">
                                    <el-option v-for="item in agents" :label="item.nickName"
                                        :value="item.id"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="店铺" prop="branchId">
                                <el-select placeholder="" clearable v-model="formData.branchId"
                                    @change="onBranchChange">
                                    <el-option v-for="item in branches" :label="item.branchName"
                                        :value="item.id"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="收银员" prop="cashierId">
                                <el-select placeholder="" clearable v-model="formData.cashierId">
                                    <el-option v-for="item in cashiers" :label="item.nickName"
                                        :value="item.id"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="游戏" prop="matchId">
                                <el-select placeholder="" clearable v-model="formData.matchId">
                                    <el-option v-for="item in EMatches" :label="item.label"
                                        :value="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="开始时间" prop="startDate">
                                <el-date-picker type="date" format="YYYY-MM-DD" clearable value-format="YYYY-MM-DD"
                                    v-model="formData.startDate" :disabled-date="disableStartDate"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="结束时间" prop="endDate">
                                <el-date-picker type="date" format="YYYY-MM-DD" clearable value-format="YYYY-MM-DD"
                                    v-model="formData.endDate" :disabled-date="disableEndDate"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label-width="0">
                                <el-button type="primary" @click="search" :disabled="loading">查询</el-button>
                                <el-button type="danger" plain @click="reset" :disabled="loading">重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
        <div class="panel">
            <div class="panel-header with-border">
                <div class="panel-title">统计结果</div>
            </div>
            <div class="panel-body" v-loading="loading">
                <el-table border :data="rows">
                    <el-table-column label="游戏" prop="matchName"></el-table-column>

                    <el-table-column label="营业额" prop="totalStake" align="right">
                        <template #default="{ row }">
                            {{ $numeral(row.totalStake) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="赔付额" prop="totalPayout" align="right">
                        <template #default="{ row }">
                            {{ $numeral(row.totalPayout) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="已兑奖" prop="totalPaid" align="right">
                        <template #default="{ row }">
                            {{ $numeral(row.totalPaid) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="未兑奖" prop="totalUnPaid" align="right">
                        <template #default="{ row }">
                            {{ $numeral(row.totalUnPaid) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="盈利" prop="profit" align="right">
                        <template #default="{ row }">
                            {{ $numeral(row.profit) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="盈利比率" prop="profitRate" align="right">
                        <template #default="{ row }">
                            {{ $numeral(row.profitRate, '0.00') }} %
                        </template>
                    </el-table-column>

                </el-table>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import AccountService from '@/api/account';
import BranchService from '@/api/branch';
import ReportService from '@/api/report';
import { useGlobalStore } from '@/store/global';
import { EMatch } from '@/typings/enum';
import { formatDate } from '@/utils';
import { getOptionsWithAll } from '@/utils/option';


const formData = reactive({
    agentId: '',
    branchId: '',
    cashierId: '',
    matchId: '',
    startDate: formatDate(new Date(), 'YYYY-MM-DD'),
    endDate: formatDate(new Date(), 'YYYY-MM-DD'),
    dateType: 10,
})

const globalStore = useGlobalStore();

const EMatches = getOptionsWithAll(EMatch);

const loading = ref(false);
const rows = ref<Array<any>>([]);

const search = () => {
    loading.value = true;
    ReportService.query(formData).then(res => {
        rows.value = res.result;
    }).finally(() => {
        loading.value = false;
    });
}

const searchForm = useTemplateRef('searchForm');
const reset = () => {
    searchForm.value?.resetFields();
    rows.value = [];
}


const onAgentChange = () => {
    formData.branchId = '';
    formData.cashierId = '';
    getBranches();
}
const onBranchChange = () => {
    formData.cashierId = '';
    getCashiers();
}


const branches = ref<Array<any>>([]);
const getBranches = () => {
    BranchService.getPaginatedList({ pageIndex: 1, pageSize: 9999, accountId: formData.agentId }).then(res => {
        branches.value = res.items;
    });
}
const cashiers = ref<Array<any>>([]);
const getCashiers = () => {
    AccountService.getPaginatedList({ pageIndex: 1, pageSize: 9999, branchId: formData.branchId, accountType: 30 }).then(res => {
        cashiers.value = res.items;
    });
}

const agents = ref<Array<any>>([]);
const getAgents = () => {
    AccountService.getPaginatedList({ pageIndex: 1, pageSize: 9999, accountType: 20 }).then(res => {
        agents.value = res.items;
    });
}

// 禁用开始日期的逻辑：如果有结束日期，则禁用晚于结束日期的日期
const disableStartDate = (date: Date) => {
    if (formData.endDate) {
        return date > new Date(formData.endDate);
    }
    return false;
}

// 禁用结束日期的逻辑：如果有开始日期，则禁用早于开始日期的日期
const disableEndDate = (date: Date) => {
    if (formData.startDate) {
        return date < new Date(formData.startDate);
    }
    return false;
}



onMounted(() => {
    if(globalStore.profileModel?.accountType == 'Admin'){
        getAgents();
    }else{
        formData.agentId = globalStore.profileModel!.id as string;
        getBranches();
    }
})
</script>

<style scoped lang="scss"></style>