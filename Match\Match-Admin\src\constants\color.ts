import { EAccountType, EOrderState } from "@/typings/enum";

export interface EnumDescription {
    value: any;
    color: string;
    type: any;
    text?: string;
}

const COLOR_LIST: EnumDescription[] = [
    { type: EOrderState, value: EOrderState.Submitted, color: "#000", text: "Submitted", },
    { type: EOrderState, value: EOrderState.Settled, color: "#2CF886", text: "Settled", },
    { type: EOrderState, value: EOrderState.UnSettled, color: "#EAB3FE", text: "UnSettled", },
    { type: EOrderState, value: EOrderState.Lose, color: "#999", text: "Lose", },
    { type: EOrderState, value: EOrderState.Canceled, color: "#ba2106", text: "Canceled", },

    { type: EAccountType, value: EAccountType.Admin, color: "#000", text: "Admin", },
    { type: EAccountType, value: EAccountType.Agent, color: "#000", text: "Agent", },
    { type: EAccountType, value: EAccountType.Cashier, color: "#000", text: "Cashier", },
];

export default COLOR_LIST;