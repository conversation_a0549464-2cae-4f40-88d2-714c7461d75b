import http from '@/utils/http';
import { AccountModel, PaginatedList, Result } from './typings';


export default class AccountService {

    public static getPaginatedList(data: {
        pageIndex: number,
        pageSize: number,
        branchId?: number | string,
        accountType?: number | string,
    }) {
        return http.post<any, PaginatedList<AccountModel>>("/api/account/fetch", data);
    }

    public static update(data: AccountModel) {
        return http.post<any, Result<AccountModel>>("/api/account/update", data);
    }
    public static create(data: AccountModel) {
        return http.post<any, Result<AccountModel>>("/api/account/create", data);
    }
    public static delete(data: AccountModel) {
        return http.post<any, Result<AccountModel>>("/api/account/delete", data);
    }
}