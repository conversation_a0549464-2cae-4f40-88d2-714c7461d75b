<template>
    <div class="page-body flex-col">
        <div class="panel">
            <div class="panel-header with-border">
                <div class="panel-title">搜索条件</div>
            </div>
            <div class="panel-body">
                <el-form :model="formData" ref="searchForm" class="search-form" label-width="70px"
                    label-position="left">
                    <el-row :gutter="20">
                        <el-col :span="5">
                            <el-form-item label="发起用户" prop="fromAccount">
                                <el-input placeholder="" v-model.trim="formData.fromAccount" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="接收店铺" prop="toBranchName">
                                <el-input placeholder="" v-model.trim="formData.toBranchName" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="接收用户" prop="toAccount">
                                <el-input placeholder="" v-model.trim="formData.toAccount" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="备注" prop="remark">
                                <el-input placeholder="" v-model.trim="formData.remark" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label-width="0">
                                <el-button type="primary" @click="search" :disabled="loading">搜索</el-button>
                                <el-button type="danger" plain @click="reset" :disabled="loading">重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
        <div class="panel">
            <div class="panel-header with-border">
                <div class="panel-title">财务记录</div>
                <div class="panel-tools">
                    <el-button type="primary" @click="view">转账</el-button>
                </div>
            </div>
            <div class="panel-body" style="padding: 0;" v-loading="loading">
                <io-table height="400" :data="rows" :pagination="pagination" @pager-change="getPaginatedList">
                    <el-table-column prop="id" label="序号" width="80" align="center">
                        <template #default="{ row, $index }">
                            {{ $index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="fromAccount.nickName" label="发起账户">
                        <template #default="{ row }">
                            {{ row.fromAccount?.loginAccount || '-' }} <template v-if="row.fromAccount?.nickName"> ({{
                                row.fromAccount?.nickName }})</template>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column prop="fromAccount.branch.branchName" label="发起店铺">
                        <template #default="{ row }">
                            {{ row.fromAccount?.branch?.branchName || '-' }}
                        </template>
                    </el-table-column> -->
                    <el-table-column prop="toAccount.branch.branchName" label="接收店铺">
                        <template #default="{ row }">
                            {{ row.toAccount?.branch?.branchName }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="toAccount.nickName" label="接收账户">
                        <template #default="{ row }">
                            {{ row.toAccount?.loginAccount }}({{ row.toAccount?.nickName }})
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="balance" label="金额">
                        <template #default="{ row }">
                            {{ $numeral(row.balance) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="remark" label="备注"  show-overflow-tooltip/>
                    <el-table-column prop="createTime" label="操作时间">
                        <template #default="{ row }">
                            {{ $moment(row.createTime) }}
                        </template>
                    </el-table-column>
                </io-table>
            </div>
        </div>
    </div>
    <transfer-module ref="TransferModuleRef" @saved="getPaginatedList"></transfer-module>

</template>

<script lang="ts" setup>
import { AssetsLogModel } from '@/api/typings';

import TransferModule from './modules/transfer-module.vue';


import AssetsService from '@/api/assets';



const formData = reactive({
    fromAccount: '',
    toAccount: '',
    toBranchName: '',
    remark: ''
})


const pagination = reactive({
    pageIndex: 1,
    pageSize: 15,
    totalCount: 0,
    totalPages: 0,
});

const search = () => {
    pagination.pageIndex = 1;
    getPaginatedList();
}

const searchForm = useTemplateRef('searchForm');
const reset = () => {
    searchForm.value?.resetFields();
    search();
}

const transferModuleRef = useTemplateRef<InstanceType<typeof TransferModule>>('TransferModuleRef');

const view = () => {
    transferModuleRef.value?.open();
}

const rows = ref<Array<AssetsLogModel>>([]);
const loading = ref(false);
const getPaginatedList = () => {
    loading.value = true;
    AssetsService.getPaginatedList({ ...formData, ...pagination }).then(res => {
        rows.value = res.items;
        pagination.pageIndex = res.pageIndex;
        pagination.totalCount = res.totalCount;
        pagination.totalPages = res.totalPages;
    }).finally(() => {
        loading.value = false;
    });
}

onMounted(() => {
    getPaginatedList();
})
</script>