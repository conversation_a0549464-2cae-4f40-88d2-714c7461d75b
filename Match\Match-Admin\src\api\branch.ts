import http from '@/utils/http';
import { BranchModel, PaginatedList, Result } from './typings';

export default class BranchService {

    public static getPaginatedList(data: {
        pageIndex: number,
        pageSize: number,
        accountId?: number |string,
        branchName?: string,
        contactName?: string,
        address?: string,
    }) {
        return http.post<any, PaginatedList<BranchModel>>("/api/branch/fetch", data);
    }

    public static update(data: BranchModel) {
        return http.post<any, Result<BranchModel>>("/api/branch/update", data);
    }

    public static create(data: BranchModel) {
        return http.post<any, Result<BranchModel>>("/api/branch/create", data);
    }
}