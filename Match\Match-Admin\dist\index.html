<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Magic</title>
    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/vConsole/3.15.1/vconsole.min.js"></script>
    <script>
      // init vConsole
      var vConsole = new VConsole();
      console.log('Hello world');
  </script> -->
    <script type="module" crossorigin src="/super/public/index-Cc7WJN9q.js"></script>
    <link rel="modulepreload" crossorigin href="/super/public/.pnpm-DuVJJfpW.js">
    <link rel="stylesheet" crossorigin href="/super/public/.pnpm-b-MUoUzb.css">
    <link rel="stylesheet" crossorigin href="/super/public/index-BWU_ZXnO.css">
    <script type="module">import.meta.url;import("_").catch(()=>1);(async function*(){})().next();if(location.protocol!="file:"){window.__vite_is_modern_browser=true}</script>
    <script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy chunks, syntax error above and the same error below should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>
  </head>
  <body>
    <div id="app"></div>
    <script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
    <script nomodule crossorigin id="vite-legacy-polyfill" src="/super/public/polyfills-legacy-CKwuPnsk.js"></script>
    <script nomodule crossorigin id="vite-legacy-entry" data-src="/super/public/index-legacy-C9rPDH0t.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>
  </body>
</html>
