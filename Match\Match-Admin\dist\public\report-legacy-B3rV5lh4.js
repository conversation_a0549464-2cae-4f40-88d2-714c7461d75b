System.register(["./.pnpm-legacy-DHcv0MF7.js","./branch-legacy-CBy-yfTD.js","./http-legacy-BkRyiHlu.js","./index-legacy-C9rPDH0t.js","./option-legacy-Dz4I8wWX.js"],(function(e,a){"use strict";var l,t,d,r,u,n,o,c,p,s,i,m,b,h,f,v,g,I,y,_,V,D,Y,P,U,M,k,w,x,S,j;return{setters:[e=>{l=e.d,t=e.O,d=e.z,r=e.A,u=e.B,n=e.o,o=e.l,c=e.p,p=e.a,s=e.w,i=e.u,m=e.P,b=e.L,h=e.T,f=e.Q,v=e.F,g=e.C,I=e.v,y=e.t,_=e.G,V=e.H,D=e.U,Y=e.x,P=e.S,U=e.c,M=e.N},e=>{k=e.A,w=e.B},e=>{x=e.h},e=>{S=e.a},e=>{j=e.g}],execute:function(){class a{static query(e){return x.post("/api/report/query",e)}}const C={class:"page-body flex-col"},N={class:"panel"},z={class:"panel-body"},F={class:"panel"},L={class:"panel-body"};e("default",l({__name:"report",setup(e){const l=t({agentId:"",branchId:"",cashierId:"",matchId:"",startDate:"",endDate:"",dateType:10}),x=j(S),T=d(!1),$=d([]),q=()=>{T.value=!0,a.query(l).then((e=>{$.value=e.result})).finally((()=>{T.value=!1}))},A=r("searchForm"),B=()=>{var e;null===(e=A.value)||void 0===e||e.resetFields(),$.value=[]},G=()=>{l.branchId="",l.cashierId="",Q()},H=()=>{l.cashierId="",J()},O=d([]),Q=()=>{w.getPaginatedList({pageIndex:1,pageSize:9999,accountId:l.agentId}).then((e=>{O.value=e.items}))},E=d([]),J=()=>{k.getPaginatedList({pageIndex:1,pageSize:9999,branchId:l.branchId,accountType:30}).then((e=>{E.value=e.items}))},K=d([]);return u((()=>{k.getPaginatedList({pageIndex:1,pageSize:9999,accountType:20}).then((e=>{K.value=e.items}))})),(e,a)=>{const t=M,d=_,r=V,u=D,k=Y,w=b,S=P,j=h,Q=f;return n(),o("div",C,[c("div",N,[a[8]||(a[8]=c("div",{class:"panel-header with-border"},[c("div",{class:"panel-title"},"报表查询")],-1)),c("div",z,[p(w,{inline:!0,ref_key:"searchForm",ref:A,model:i(l),class:"search-form"},{default:s((()=>[p(r,{label:"代理",prop:"agentId"},{default:s((()=>[p(d,{placeholder:"",clearable:"",modelValue:i(l).agentId,"onUpdate:modelValue":a[0]||(a[0]=e=>i(l).agentId=e),onChange:G},{default:s((()=>[(n(!0),o(v,null,g(i(K),(e=>(n(),U(t,{label:e.nickName,value:e.id},null,8,["label","value"])))),256))])),_:1},8,["modelValue"])])),_:1}),p(r,{label:"店铺",prop:"branchId"},{default:s((()=>[p(d,{placeholder:"",clearable:"",modelValue:i(l).branchId,"onUpdate:modelValue":a[1]||(a[1]=e=>i(l).branchId=e),onChange:H},{default:s((()=>[(n(!0),o(v,null,g(i(O),(e=>(n(),U(t,{label:e.branchName,value:e.id},null,8,["label","value"])))),256))])),_:1},8,["modelValue"])])),_:1}),p(r,{label:"收银员",prop:"cashierId"},{default:s((()=>[p(d,{placeholder:"",clearable:"",modelValue:i(l).cashierId,"onUpdate:modelValue":a[2]||(a[2]=e=>i(l).cashierId=e)},{default:s((()=>[(n(!0),o(v,null,g(i(E),(e=>(n(),U(t,{label:e.nickName,value:e.id},null,8,["label","value"])))),256))])),_:1},8,["modelValue"])])),_:1}),p(r,{label:"游戏",prop:"matchId"},{default:s((()=>[p(d,{placeholder:"",clearable:"",modelValue:i(l).matchId,"onUpdate:modelValue":a[3]||(a[3]=e=>i(l).matchId=e)},{default:s((()=>[(n(!0),o(v,null,g(i(x),(e=>(n(),U(t,{label:e.label,value:e.value},null,8,["label","value"])))),256))])),_:1},8,["modelValue"])])),_:1}),p(r,{label:"开始时间",prop:"startDate"},{default:s((()=>[p(u,{type:"date",format:"YYYY-MM-DD",clearable:"","value-format":"YYYY-MM-DD",modelValue:i(l).startDate,"onUpdate:modelValue":a[4]||(a[4]=e=>i(l).startDate=e)},null,8,["modelValue"])])),_:1}),p(r,{label:"结束时间",prop:"endDate"},{default:s((()=>[p(u,{type:"date",format:"YYYY-MM-DD",clearable:"","value-format":"YYYY-MM-DD",modelValue:i(l).endDate,"onUpdate:modelValue":a[5]||(a[5]=e=>i(l).endDate=e)},null,8,["modelValue"])])),_:1}),p(r,null,{default:s((()=>[p(k,{type:"primary",onClick:q,disabled:i(T)},{default:s((()=>a[6]||(a[6]=[I("查询")]))),_:1},8,["disabled"]),p(k,{type:"danger",plain:"",onClick:B,disabled:i(T)},{default:s((()=>a[7]||(a[7]=[I("重置")]))),_:1},8,["disabled"])])),_:1})])),_:1},8,["model"])])]),c("div",F,[a[9]||(a[9]=c("div",{class:"panel-header with-border"},[c("div",{class:"panel-title"},"统计结果")],-1)),m((n(),o("div",L,[p(j,{border:"",data:i($)},{default:s((()=>[p(S,{label:"游戏",prop:"matchName"}),p(S,{label:"营业额",prop:"totalStake"},{default:s((({row:a})=>[I(y(e.$numeral(a.totalStake)),1)])),_:1}),p(S,{label:"赔付额",prop:"totalPayout"},{default:s((({row:a})=>[I(y(e.$numeral(a.totalPayout)),1)])),_:1}),p(S,{label:"已兑奖",prop:"totalPaid"},{default:s((({row:a})=>[I(y(e.$numeral(a.totalPaid)),1)])),_:1}),p(S,{label:"未兑奖",prop:"totalUnPaid"},{default:s((({row:a})=>[I(y(e.$numeral(a.totalUnPaid)),1)])),_:1})])),_:1},8,["data"])])),[[Q,i(T)]])])])}}}))}}}));
