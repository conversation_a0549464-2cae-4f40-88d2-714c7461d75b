<template>
    <el-dialog v-model="visible" title="游戏配置" width="600" draggable :close-on-click-modal="false"
        :close-on-press-escape="false">

        <el-form label-width="100px">
            <el-form-item label="游戏名">
                <el-input placeholder="" readonly v-model="formData.matchName" />
            </el-form-item>
           
            <el-form-item label="开奖配置" v-if="formData.configKey == 'ColorLucky_Rule'">
                <el-checkbox label="8中8" v-model="formData.jsonValue['8_8']"></el-checkbox>
                <el-checkbox label="8中7" v-model="formData.jsonValue['8_7']"></el-checkbox>
                <el-checkbox label="7中7" v-model="formData.jsonValue['7_7']"></el-checkbox>
                <el-checkbox label="7中6" v-model="formData.jsonValue['7_6']"></el-checkbox>
                <el-checkbox label="6中6" v-model="formData.jsonValue['6_6']"></el-checkbox>
                <el-checkbox label="6中5" v-model="formData.jsonValue['6_5']"></el-checkbox>
                <el-checkbox label="5中5" v-model="formData.jsonValue['5_5']"></el-checkbox>
            </el-form-item>
             <el-form-item label="吃分比例(%)" v-else>
                <el-input-number :min="-20" :max="50" :step="1" style="width: 100%;"
                    v-model="formData.jsonValue.reserveRatio"></el-input-number>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="justify-center">
                <el-button type="danger" :disabled="loading" plain @click="close">取消</el-button>
                <el-button type="primary" :disabled="loading" @click="save">保存</el-button>
            </div>
        </template>
    </el-dialog>
</template>


<script lang="ts" setup>
import ConfigService from '@/api/config';
import { ConfigModel, MatchModel } from '@/api/typings';
import { cloneDeep } from 'lodash';


const loading = ref(false);
const visible = ref(false);

const formData = ref<ConfigModel>({} as ConfigModel);
const emits = defineEmits(['saved']);

const getConfig = (row: MatchModel) => {

    const data = {
        configKey: '',
        matchName: row.matchName
    };

    switch (row.id) {
        case 1000:
            data.configKey = 'LuckyRouletten_Rule';
            break;
        case 2000:
            data.configKey = 'CarsRacing_Rule';
            break;
        case 3000:
            data.configKey = 'ColorLucky_Rule';
            break;
    }
    formData.value.matchName = row.matchName;


    ConfigService.get(data).then(res => {
        formData.value = res.result;
        formData.value.jsonValue = JSON.parse(res.result.configValue);
        if (formData.value.jsonValue.reserveRatio) {
            formData.value.jsonValue.reserveRatio = formData.value.jsonValue.reserveRatio * 100;
        }
        formData.value.matchName = row.matchName;
        visible.value = true;
    });
}

const open = (row: MatchModel) => {
    getConfig(row);
}


const save = () => {


    const data = cloneDeep(formData.value);
    if (data.jsonValue.reserveRatio) {
        data.jsonValue.reserveRatio = data.jsonValue.reserveRatio / 100;
    }
    data.configValue = JSON.stringify(data.jsonValue);

    ConfigService.update(data).then(res => {
        visible.value = false;
        emits('saved', data);
    });
}

const close = () => {
    visible.value = false;
}


defineExpose({
    open: open
});
</script>