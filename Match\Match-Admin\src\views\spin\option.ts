import { MatchOption } from "@/api/typings";

const MatchOptions: MatchOption[] =  [
    {
        "id": 1001,
        "matchId": 1001,
        "odds": 6,
        "optionName": "A",
        "type": "Sector",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1002,
        "matchId": 1001,
        "odds": 6,
        "optionName": "B",
        "type": "Sector",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1003,
        "matchId": 1001,
        "odds": 6,
        "optionName": "C",
        "type": "Sector",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1004,
        "matchId": 1001,
        "odds": 6,
        "optionName": "D",
        "type": "Sector",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1005,
        "matchId": 1001,
        "odds": 6,
        "optionName": "E",
        "type": "Sector",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1006,
        "matchId": 1001,
        "odds": 6,
        "optionName": "F",
        "type": "Sector",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1007,
        "matchId": 1001,
        "odds": 36,
        "optionName": "0",
        "type": "ExactNumber",
        "style": "green",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1008,
        "matchId": 1001,
        "odds": 36,
        "optionName": "1",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1009,
        "matchId": 1001,
        "odds": 36,
        "optionName": "2",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1010,
        "matchId": 1001,
        "odds": 36,
        "optionName": "3",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1011,
        "matchId": 1001,
        "odds": 36,
        "optionName": "4",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1012,
        "matchId": 1001,
        "odds": 36,
        "optionName": "5",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1013,
        "matchId": 1001,
        "odds": 36,
        "optionName": "6",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1014,
        "matchId": 1001,
        "odds": 36,
        "optionName": "7",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1015,
        "matchId": 1001,
        "odds": 36,
        "optionName": "8",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1016,
        "matchId": 1001,
        "odds": 36,
        "optionName": "9",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1017,
        "matchId": 1001,
        "odds": 36,
        "optionName": "10",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1018,
        "matchId": 1001,
        "odds": 36,
        "optionName": "11",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1019,
        "matchId": 1001,
        "odds": 36,
        "optionName": "12",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1020,
        "matchId": 1001,
        "odds": 36,
        "optionName": "13",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1021,
        "matchId": 1001,
        "odds": 36,
        "optionName": "14",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1022,
        "matchId": 1001,
        "odds": 36,
        "optionName": "15",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1023,
        "matchId": 1001,
        "odds": 36,
        "optionName": "16",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1024,
        "matchId": 1001,
        "odds": 36,
        "optionName": "17",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1025,
        "matchId": 1001,
        "odds": 36,
        "optionName": "18",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1026,
        "matchId": 1001,
        "odds": 36,
        "optionName": "19",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1027,
        "matchId": 1001,
        "odds": 36,
        "optionName": "20",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1028,
        "matchId": 1001,
        "odds": 36,
        "optionName": "21",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1029,
        "matchId": 1001,
        "odds": 36,
        "optionName": "22",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1030,
        "matchId": 1001,
        "odds": 36,
        "optionName": "23",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1031,
        "matchId": 1001,
        "odds": 36,
        "optionName": "24",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1032,
        "matchId": 1001,
        "odds": 36,
        "optionName": "25",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1033,
        "matchId": 1001,
        "odds": 36,
        "optionName": "26",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1034,
        "matchId": 1001,
        "odds": 36,
        "optionName": "27",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1035,
        "matchId": 1001,
        "odds": 36,
        "optionName": "28",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1036,
        "matchId": 1001,
        "odds": 36,
        "optionName": "29",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1037,
        "matchId": 1001,
        "odds": 36,
        "optionName": "30",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1038,
        "matchId": 1001,
        "odds": 36,
        "optionName": "31",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1039,
        "matchId": 1001,
        "odds": 36,
        "optionName": "32",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1040,
        "matchId": 1001,
        "odds": 36,
        "optionName": "33",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1041,
        "matchId": 1001,
        "odds": 36,
        "optionName": "34",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1042,
        "matchId": 1001,
        "odds": 36,
        "optionName": "35",
        "type": "ExactNumber",
        "style": "black",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1043,
        "matchId": 1001,
        "odds": 36,
        "optionName": "36",
        "type": "ExactNumber",
        "style": "red",
        "createTime": "2025-04-15T15:06:46+08:00"
    },
    {
        "id": 1044,
        "matchId": 1001,
        "odds": 3,
        "optionName": "1~12",
        "type": "Dozens",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
      },
      {
        "id": 1045,
        "matchId": 1001,
        "odds": 3,
        "optionName": "13~24",
        "type": "Dozens",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
      },
      {
        "id": 1046,
        "matchId": 1001,
        "odds": 3,
        "optionName": "25~36",
        "type": "Dozens",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
      },
      {
        "id": 1047,
        "matchId": 1001,
        "odds": 2,
        "optionName": "Even",
        "type": "Other/Even",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
      },
      {
        "id": 1048,
        "matchId": 1001,
        "odds": 2,
        "optionName": "Odd",
        "type": "Other/Odd",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
      },
      {
        "id": 1049,
        "matchId": 1001,
        "odds": 2,
        "optionName": "Red",
        "type": "Other/Colors",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
      },
      {
        "id": 1050,
        "matchId": 1001,
        "odds": 2,
        "optionName": "Black",
        "type": "Other/Colors",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
      },
      {
        "id": 1051,
        "matchId": 1001,
        "odds": 2,
        "optionName": "1-18",
        "type": "Other/Low",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
      },
      {
        "id": 1052,
        "matchId": 1001,
        "odds": 2,
        "optionName": "19-36",
        "type": "Other/High",
        "style": "gray",
        "createTime": "2025-04-15T15:06:46+08:00"
      }
]
export default MatchOptions;