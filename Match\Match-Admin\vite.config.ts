import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path';
import legacy from '@vitejs/plugin-legacy';

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    createSvgIconsPlugin({
      // 指定目录(svg存放目录）
      iconDirs: [resolve(process.cwd(), 'src/assets/icon')],
      // 使用 svg 图标的格式（name为图片名称）
      symbolId: 'icon-[name]',
      //生成组件插入位置 只有两个值 body-last | body-first
      inject: 'body-last'
    }),
    AutoImport({
      imports: ['vue', 'vue-router'],
      resolvers: [ElementPlusResolver({
        importStyle: 'sass'
      })],
      dts: 'typings/auto-import.d.ts',
    }),
    Components({
      resolvers: [ElementPlusResolver({
        importStyle: 'sass',
        directives: true
      })],
      dts: 'typings/components.d.ts'
    }),
    legacy({
      targets: ["defaults", "not IE 11", 'chromeAndroid>=52, iOS>=13.1'],
    })
  ],
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern-compiler',
        additionalData: `@use "@/assets/style/element/index.scss" as *;`,
      }
    }
  },
  server: {
    host: '0.0.0.0',
    port: 8088,
    open: true
  },
  base: '/super/',
  resolve: {
    alias: [
      {
        find: '@',
        replacement: resolve(__dirname, 'src')
      }
    ],
    extensions: ['.vue', '.ts', '.js', '.json']
  },
  build: {
    assetsDir: 'public',
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks(id) {
          // return id.includes('node_modules') ? 'vender' : 'app';
          if (id.includes("node_modules")) {
            // 让每个插件都打包成独立的文件
            return id.toString().split("node_modules/")[1].split("/")[0].toString();
          }
        }
      }
    }
  }
})
