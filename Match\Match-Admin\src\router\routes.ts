import { RouteRecordRaw } from 'vue-router';

const routes: Array<RouteRecordRaw> = [
    {
        path: '/',
        name: '',
        redirect: '/dashboard',
        component: () => import('@/views/layout/layout.vue'),
        children: [
            {
                path: 'dashboard',
                name: 'dashboard',
                component: () => import('@/views/dashboard/dashboard.vue')
            },
            {
                path: 'assets',
                name: 'assets',
                component: () => import('@/views/assets/assets.vue')
            },
            {
                path: 'report',
                name: 'report',
                component: () => import('@/views/report/report.vue')
            },
            {
                path: 'account',
                name: 'account',
                component: () => import('@/views/account/account.vue')
            },
            {
                path: 'branch',
                name: 'branch',
                component: () => import('@/views/branch/branch.vue')
            },
            {
                path: 'match',
                name: 'match',
                component: () => import('@/views/match/match.vue')
            }
        ]
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/views/passport/login.vue'),
    },
    // fallback router
    // {
    //     path: '/:w+',
    //     component: () => import('@/views/layout/fallback/404.vue'),
    // }
]

export default routes;
