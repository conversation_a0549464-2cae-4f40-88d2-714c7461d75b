import{d as z,z as A,A as U,B as $,o as v,c as T,w as a,p as f,a as e,v as p,u as t,l as x,C as D,F as S,D as W,G as X,H as O,I as Y,J as j,L as G,x as H,M as Z,N as ee,O as R,P as ae,Q as le,t as b,R as te,S as oe}from"./.pnpm-DuVJJfpW.js";import{_ as ne}from"./io-table-DC_2HcLS.js";import{h as q}from"./http-QDC4lyIP.js";import{A as re,B as se}from"./branch-zzdSpi4p.js";import{E as de}from"./index-Cc7WJN9q.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";class J{static getPaginatedList(u){return q.post("/api/assets/fetch",u)}static transfer(u){return q.post("/api/assets/transfer",u)}}const ue={class:"justify-center"},ce=z({__name:"transfer-module",emits:["saved"],setup(w,{expose:u,emit:g}){const h=A(!1),_=A(!1),F=g,c=A({balance:0}),C=U("form"),E=()=>{var n;(n=C.value)==null||n.validate((r,B)=>{r&&(h.value=!0,J.transfer(c.value).then(l=>{_.value=!1,F("saved",c.value)}).finally(()=>{h.value=!1}))})},I=()=>{var n;(n=C.value)==null||n.resetFields()},y=()=>{_.value=!0},i=()=>{_.value=!1},o=()=>{k(),c.value.toId=""},V=A([]),k=()=>{re.getPaginatedList({pageIndex:1,pageSize:99999,accountType:de.Cashier,branchId:c.value.branchId}).then(n=>{V.value=n.items})},N=A([]),M=()=>{se.getPaginatedList({pageIndex:1,pageSize:99999}).then(n=>{N.value=n.items})};return $(()=>{M()}),u({open:y}),(n,r)=>{const B=ee,l=X,s=O,m=Y,P=j,Q=G,L=H,K=Z;return v(),T(K,{modelValue:t(_),"onUpdate:modelValue":r[4]||(r[4]=d=>W(_)?_.value=d:null),title:"转账信息",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1,onClosed:I},{footer:a(()=>[f("div",ue,[e(L,{type:"danger",disabled:t(h),plain:"",onClick:i},{default:a(()=>r[5]||(r[5]=[p("取消")])),_:1},8,["disabled"]),e(L,{type:"primary",disabled:t(h),onClick:E},{default:a(()=>r[6]||(r[6]=[p("保存")])),_:1},8,["disabled"])])]),default:a(()=>[e(Q,{"label-width":"100px",model:t(c),ref_key:"form",ref:C,"show-message":!1},{default:a(()=>[e(s,{label:"接收店铺",required:"",prop:"branchId"},{default:a(()=>[e(l,{modelValue:t(c).branchId,"onUpdate:modelValue":r[0]||(r[0]=d=>t(c).branchId=d),placeholder:"",clearable:"",filterable:"",onChange:o},{default:a(()=>[(v(!0),x(S,null,D(t(N),d=>(v(),T(B,{key:d.id,label:d.branchName,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"接收账户",required:"",prop:"toId"},{default:a(()=>[e(l,{modelValue:t(c).toId,"onUpdate:modelValue":r[1]||(r[1]=d=>t(c).toId=d),placeholder:"",clearable:"",filterable:""},{default:a(()=>[(v(!0),x(S,null,D(t(V),d=>(v(),T(B,{key:d.id,label:d.nickName,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"金额",required:"",prop:"balance"},{default:a(()=>[e(m,{placeholder:"",modelValue:t(c).balance,"onUpdate:modelValue":r[2]||(r[2]=d=>t(c).balance=d)},null,8,["modelValue"])]),_:1}),e(s,{label:"备注",required:"",prop:"remark"},{default:a(()=>[e(P,{placeholder:"",modelValue:t(c).remark,"onUpdate:modelValue":r[3]||(r[3]=d=>t(c).remark=d),type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}}),ie={class:"page-body flex-col"},me={class:"panel"},pe={class:"panel-body"},fe={class:"panel"},_e={class:"panel-header with-border"},be={class:"panel-tools"},ge={class:"panel-body",style:{padding:"0"}},Ne=z({__name:"assets",setup(w){const u=R({fromAccount:"",toAccount:"",toBranchName:"",remark:""}),g=R({pageIndex:1,pageSize:15,totalCount:0,totalPages:0}),h=()=>{g.pageIndex=1,y()},_=U("searchForm"),F=()=>{var i;(i=_.value)==null||i.resetFields(),h()},c=U("TransferModuleRef"),C=()=>{var i;(i=c.value)==null||i.open()},E=A([]),I=A(!1),y=()=>{I.value=!0,J.getPaginatedList({...u,...g}).then(i=>{E.value=i.items,g.pageIndex=i.pageIndex,g.totalCount=i.totalCount,g.totalPages=i.totalPages}).finally(()=>{I.value=!1})};return $(()=>{y()}),(i,o)=>{const V=j,k=O,N=H,M=G,n=oe,r=ne,B=le;return v(),x(S,null,[f("div",ie,[f("div",me,[o[6]||(o[6]=f("div",{class:"panel-header with-border"},[f("div",{class:"panel-title"},"搜索条件")],-1)),f("div",pe,[e(M,{model:t(u),inline:!0,ref_key:"searchForm",ref:_,class:"search-form"},{default:a(()=>[e(k,{label:"发起用户名",prop:"fromAccount"},{default:a(()=>[e(V,{placeholder:"",modelValue:t(u).fromAccount,"onUpdate:modelValue":o[0]||(o[0]=l=>t(u).fromAccount=l),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(k,{label:"接收用户名",prop:"toAccount"},{default:a(()=>[e(V,{placeholder:"",modelValue:t(u).toAccount,"onUpdate:modelValue":o[1]||(o[1]=l=>t(u).toAccount=l),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(k,{label:"接收店铺",prop:"toBranchName"},{default:a(()=>[e(V,{placeholder:"",modelValue:t(u).toBranchName,"onUpdate:modelValue":o[2]||(o[2]=l=>t(u).toBranchName=l),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(k,{label:"备注",prop:"remark"},{default:a(()=>[e(V,{placeholder:"",modelValue:t(u).remark,"onUpdate:modelValue":o[3]||(o[3]=l=>t(u).remark=l),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e(k,null,{default:a(()=>[e(N,{type:"primary",onClick:h},{default:a(()=>o[4]||(o[4]=[p("搜索")])),_:1}),e(N,{type:"danger",plain:"",onClick:F},{default:a(()=>o[5]||(o[5]=[p("重置")])),_:1})]),_:1})]),_:1},8,["model"])])]),f("div",fe,[f("div",_e,[o[8]||(o[8]=f("div",{class:"panel-title"},"财务记录",-1)),f("div",be,[e(N,{type:"primary",onClick:C},{default:a(()=>o[7]||(o[7]=[p("转账")])),_:1})])]),ae((v(),x("div",ge,[e(r,{height:"500",data:t(E),pagination:t(g),onPagerChange:y},{default:a(()=>[e(n,{prop:"id",label:"序号"},{default:a(({row:l,$index:s})=>[p(b(s+1),1)]),_:1}),e(n,{prop:"fromAccount.nickName",label:"发起账户"},{default:a(({row:l})=>{var s,m,P;return[p(b(((s=l.fromAccount)==null?void 0:s.loginAccount)||"-")+" ",1),(m=l.fromAccount)!=null&&m.nickName?(v(),x(S,{key:0},[p(" ("+b((P=l.fromAccount)==null?void 0:P.nickName)+")",1)],64)):te("",!0)]}),_:1}),e(n,{prop:"fromAccount.branch.branchName",label:"发起店铺"},{default:a(({row:l})=>{var s,m;return[p(b(((m=(s=l.fromAccount)==null?void 0:s.branch)==null?void 0:m.branchName)||"-"),1)]}),_:1}),e(n,{prop:"toAccount.nickName",label:"接收账户"},{default:a(({row:l})=>{var s,m;return[p(b((s=l.toAccount)==null?void 0:s.loginAccount)+"("+b((m=l.toAccount)==null?void 0:m.nickName)+") ",1)]}),_:1}),e(n,{prop:"toAccount.branch.branchName",label:"接收店铺"},{default:a(({row:l})=>{var s,m;return[p(b((m=(s=l.toAccount)==null?void 0:s.branch)==null?void 0:m.branchName),1)]}),_:1}),e(n,{prop:"balance",label:"金额","header-align":"center",align:"right"},{default:a(({row:l})=>[p(b(i.$numeral(l.balance)),1)]),_:1}),e(n,{prop:"remark",label:"备注"}),e(n,{prop:"createBy",label:"操作人"}),e(n,{prop:"createTime",label:"操作时间"},{default:a(({row:l})=>[p(b(i.$moment(l.createTime)),1)]),_:1})]),_:1},8,["data","pagination"])])),[[B,t(I)]])])]),e(ce,{ref:"TransferModuleRef",onSaved:y},null,512)],64)}}});export{Ne as default};
