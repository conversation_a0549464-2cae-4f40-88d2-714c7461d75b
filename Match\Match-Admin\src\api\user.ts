import http from '@/utils/http';
import { PaginatedList, ProfileModel, Result } from './typings';


export default class UserService {

    public static login(data: {
        loginAccount: string,
        loginPassword: string
    }): Promise<Result<ProfileModel>> {
        return http.post("api/passport/login", data);
    }


    public static getProfile() {
        return http.get<any, Result<ProfileModel>>("api/passport/profile");
    }

    public static getPaginatedList(data: {
        pageIndex: number,
        pageSize: number,
        matchId?: number,
        round?: number,
        cashier?: string
    }) {
        return http.post<PaginatedList<OrderModel>>("/order/fetch", data);
    }
}