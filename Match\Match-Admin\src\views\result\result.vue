<template>
    <div class="page-body flex-col">
        <div class="panel scan">
            <div class="panel-header with-border">
                <div class="panel-title">Scan</div>
            </div>
            <div class="panel-body">
                <el-input ref="input" v-model="barcode" size="large" @keyup.enter="handleScan" />
                <p class="tips">Please scan the barcode with the scanner when the input is focused</p>
            </div>
        </div>
        <!-- 
        <div class="panel scan">
            <div class="panel-header with-border">
                <div class="panel-title">Recent Tickets</div>
            </div>
            <div class="panel-body">
               
            </div>
        </div> -->
    </div>
    <ticket-dialog ref="TicketDialogRef"></ticket-dialog>
</template>

<script lang="ts" setup>

import TicketDialog from '../ticket/ticket-dialog.vue';

import OrderService from '@/api/order';

const inputRef = useTemplateRef("input");
onMounted(() => {
    inputRef.value?.focus();

});


const barcode = ref('');
const ticketDialogRef = useTemplateRef<InstanceType<typeof TicketDialog>>('TicketDialogRef');
const handleScan = () => {
    OrderService.getByOrderNo({ orderNo: barcode.value }).then(res => {
        ticketDialogRef.value?.openWithOrder(res.result);
    }).finally(() => {
        barcode.value = '';
    });
};
</script>

<style lang="scss" scoped>
.scan {
    width: 600px;
}
</style>