import{d as w,k as v,O as y,z as V,A as b,o as x,l as A,p as m,a as l,w as a,u as s,a2 as k,L as E,v as F,J as P,H as z,x as B}from"./.pnpm-DuVJJfpW.js";import{u as C}from"./global-BZMqDQCr.js";import{_ as I}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./http-QDC4lyIP.js";import"./index-Cc7WJN9q.js";const L={class:"full-page login-page"},N={class:"login-body"},q=w({__name:"login",setup(R){k();const c=v(()=>e.loginAccount===""||e.loginPassword===""),e=y({loginAccount:"sysadmin",loginPassword:"********"}),n=V(!1),{userLogin:p}=C(),u=b("loginForm"),g=async()=>{var t;(t=u.value)==null||t.validate(async(o,r)=>{if(o)try{n.value=!0,p(e)}finally{n.value=!1}})};return(t,o)=>{const r=P,i=z,f=B,_=E;return x(),A("div",L,[m("div",N,[o[3]||(o[3]=m("div",{class:"login-title"}," Magic ",-1)),l(_,{model:s(e),"show-message":!1,class:"login-form",ref_key:"loginForm",ref:u},{default:a(()=>[l(i,{label:"",prop:"loginAccount",class:"input",required:""},{default:a(()=>[l(r,{placeholder:"username",modelValue:s(e).loginAccount,"onUpdate:modelValue":o[0]||(o[0]=d=>s(e).loginAccount=d),size:"large"},null,8,["modelValue"])]),_:1}),l(i,{label:"",prop:"loginPassword",class:"input",required:""},{default:a(()=>[l(r,{placeholder:"password",type:"password",modelValue:s(e).loginPassword,"onUpdate:modelValue":o[1]||(o[1]=d=>s(e).loginPassword=d),size:"large"},null,8,["modelValue"])]),_:1}),l(i,{class:"input"},{default:a(()=>[l(f,{type:"primary",disabled:s(c),loading:s(n),class:"full-width",size:"large",onClick:g},{default:a(()=>o[2]||(o[2]=[F(" Login ")])),_:1},8,["disabled","loading"])]),_:1})]),_:1},8,["model"])])])}}}),J=I(q,[["__scopeId","data-v-af24d1ee"]]);export{J as default};
