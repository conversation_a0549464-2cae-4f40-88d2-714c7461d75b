const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["public/layout-BhJU2Gxo.js","public/_plugin-vue_export-helper-DlAUqK2U.js","public/.pnpm-DuVJJfpW.js","public/.pnpm-b-MUoUzb.css","public/global-BZMqDQCr.js","public/http-QDC4lyIP.js","public/layout-BCotprp7.css","public/dashboard-BWbnCB6F.js","public/assets-B6W-iKSF.js","public/io-table-DC_2HcLS.js","public/io-table-C8NKHXoQ.css","public/branch-zzdSpi4p.js","public/report-BUkHVBe_.js","public/option-CWv3DsyR.js","public/account-LcFefImA.js","public/branch-Dd7UmTA9.js","public/match-BrG224pM.js","public/login-BEGk0gT6.js","public/login-Cf8hmG2M.css"])))=>i.map(i=>d[i]);
import{d as x,o as f,c as h,r as _,w as D,a as R,E as V,b as I,e as j,f as B,g as E,h as S,s as T,n as v,i as y,j as H}from"./.pnpm-DuVJJfpW.js";function Q(){import.meta.url,import("_").catch(()=>1),async function*(){}().next()}(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const r of n)if(r.type==="childList")for(const i of r.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function t(n){const r={};return n.integrity&&(r.integrity=n.integrity),n.referrerPolicy&&(r.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?r.credentials="include":n.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(n){if(n.ep)return;n.ep=!0;const r=t(n);fetch(n.href,r)}})();const M=x({__name:"App",setup(e){return(o,t)=>{const s=_("router-view"),n=_("OrderDialog"),r=V;return f(),h(r,null,{default:D(()=>[(f(),h(s,{key:o.$route.fullPath})),R(n,{ref:"orderDialog"},null,512)]),_:1})}}}),O="modulepreload",U=function(e){return"/super/"+e},g={},d=function(o,t,s){let n=Promise.resolve();if(t&&t.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),l=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));n=Promise.allSettled(t.map(c=>{if(c=U(c),c in g)return;g[c]=!0;const p=c.endsWith(".css"),C=p?'[rel="stylesheet"]':"";if(document.querySelector('link[href="'.concat(c,'"]').concat(C)))return;const u=document.createElement("link");if(u.rel=p?"stylesheet":O,p||(u.as="script"),u.crossOrigin="",u.href=c,l&&u.setAttribute("nonce",l),document.head.appendChild(u),p)return new Promise((P,A)=>{u.addEventListener("load",P),u.addEventListener("error",()=>A(new Error("Unable to preload CSS for ".concat(c))))})}))}function r(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return n.then(i=>{for(const l of i||[])l.status==="rejected"&&r(l.reason);return o().catch(r)})},$=[{path:"/",name:"",redirect:"/dashboard",component:()=>d(()=>import("./layout-BhJU2Gxo.js"),__vite__mapDeps([0,1,2,3,4,5,6])),children:[{path:"dashboard",name:"dashboard",component:()=>d(()=>import("./dashboard-BWbnCB6F.js"),__vite__mapDeps([7,1,2,3]))},{path:"assets",name:"assets",component:()=>d(()=>import("./assets-B6W-iKSF.js"),__vite__mapDeps([8,2,3,9,1,10,5,11]))},{path:"report",name:"report",component:()=>d(()=>import("./report-BUkHVBe_.js"),__vite__mapDeps([12,2,3,11,5,13]))},{path:"account",name:"account",component:()=>d(()=>import("./account-LcFefImA.js"),__vite__mapDeps([14,2,3,9,1,10,11,5,13]))},{path:"branch",name:"branch",component:()=>d(()=>import("./branch-Dd7UmTA9.js"),__vite__mapDeps([15,2,3,9,1,10,11,5]))},{path:"match",name:"match",component:()=>d(()=>import("./match-BrG224pM.js"),__vite__mapDeps([16,2,3,9,1,10,5]))}]},{path:"/login",name:"login",component:()=>d(()=>import("./login-BEGk0gT6.js"),__vite__mapDeps([17,2,3,4,5,1,18]))}],Z="MATCH.SUPER.Token",w=I({history:j(),routes:$,scrollBehavior(e,o,t){return{top:0}}});w.beforeEach(async(e,o,t)=>{const s=B.get(Z);if(s&&e.path=="/login"){t("/");return}if(!s&&e.path!="/login"){t("/login");return}t()});if(typeof window<"u"){let e=function(){var o=document.body,t=document.getElementById("__svg__icons__dom__");t||(t=document.createElementNS("http://www.w3.org/2000/svg","svg"),t.style.position="absolute",t.style.width="0",t.style.height="0",t.id="__svg__icons__dom__",t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink")),t.innerHTML='<symbol viewBox="0 0 48 48" fill="none"  id="icon-close"><path d="M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Z" stroke="currentColor" stroke-width="4" stroke-linejoin="round" /><path d="M29.657 18.343 18.343 29.657M18.343 18.343l11.314 11.314" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" /></symbol><symbol viewBox="0 0 48 48" fill="none"  id="icon-home"><path d="M9 18v24h30V18L24 6 9 18Z" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" /><path d="M19 29v13h10V29H19Z" stroke="#333" stroke-width="4" stroke-linejoin="round" /><path d="M9 42h30" stroke="#333" stroke-width="4" stroke-linecap="round" /></symbol><symbol viewBox="0 0 48 48" fill="none"  id="icon-order"><path d="M33.05 7H38a2 2 0 0 1 2 2v33a2 2 0 0 1-2 2H10a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h7v3h14V7h2.05Z" stroke="currentColor" stroke-width="4" stroke-linejoin="round" /><path stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M17 4h14v6H17zM27 19l-8 8.001h10.004l-8.004 8" /></symbol><symbol viewBox="0 0 48 48" fill="none"  id="icon-power"><path d="M14.5 8a19.05 19.05 0 0 0-4.75 3.84C6.794 15.146 5 19.49 5 24.245 5 34.603 13.507 43 24 43s19-8.397 19-18.755c0-4.756-1.794-9.099-4.75-12.405A19.02 19.02 0 0 0 33.5 8M24 4v20" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" /></symbol><symbol viewBox="0 0 48 48"  id="icon-setting"><path d="M18.284 43.171a19.995 19.995 0 0 1-8.696-5.304 6 6 0 0 0-5.182-9.838A20.09 20.09 0 0 1 4 24c0-2.09.32-4.106.916-6H5a6 6 0 0 0 5.385-8.65 19.968 19.968 0 0 1 8.267-4.627A6 6 0 0 0 24 8a6 6 0 0 0 5.348-3.277 19.968 19.968 0 0 1 8.267 4.627A6 6 0 0 0 43.084 18c.595 1.894.916 3.91.916 6 0 1.38-.14 2.728-.406 4.03a6 6 0 0 0-5.182 9.838 19.995 19.995 0 0 1-8.696 5.303 6.003 6.003 0 0 0-11.432 0Z" fill="none" stroke="currentColor" stroke-width="4" stroke-linejoin="round" /><path d="M24 31a7 7 0 1 0 0-14 7 7 0 0 0 0 14Z" fill="none" stroke="#333" stroke-width="4" stroke-linejoin="round" /></symbol>',o.insertBefore(t,o.lastChild)};document.readyState==="loading"?document.addEventListener("DOMContentLoaded",e):e()}const b=E({locale:localStorage.getItem("lang")||"en",globalInjection:!0,legacy:!1}),L=S();L.use(T);function W(e,{locale:o="en"}={}){v.locale(o),e.config.globalProperties.$numeral=(t,s="0,0")=>v(t).format(s)}function Y(e,{locale:o="en"}={}){y.locale(o),e.config.globalProperties.$moment=(t,s="YYYY/MM/DD HH:mm")=>y(t).format(s)}var q=(e=>(e[e.LuckyRoulette=1e3]="LuckyRoulette",e[e.VirtualRace=2e3]="VirtualRace",e[e.ColorLucky=3e3]="ColorLucky",e))(q||{}),a=(e=>(e[e.Submitted=0]="Submitted",e[e.UnSettled=10]="UnSettled",e[e.WinPart=11]="WinPart",e[e.Lose=20]="Lose",e[e.Canceled=30]="Canceled",e[e.Settled=40]="Settled",e))(a||{}),F=(e=>(e[e.Normal=10]="Normal",e[e.Disabled=20]="Disabled",e))(F||{}),m=(e=>(e[e.Admin=10]="Admin",e[e.Agent=20]="Agent",e[e.Cashier=30]="Cashier",e))(m||{});const N=[{type:a,value:a.Submitted,color:"#000",text:"Submitted"},{type:a,value:a.Settled,color:"#2CF886",text:"Settled"},{type:a,value:a.UnSettled,color:"#EAB3FE",text:"UnSettled"},{type:a,value:a.Lose,color:"#999",text:"Lose"},{type:a,value:a.Canceled,color:"#ba2106",text:"Canceled"},{type:m,value:m.Admin,color:"#000",text:"Admin"},{type:m,value:m.Agent,color:"#000",text:"Agent"},{type:m,value:m.Cashier,color:"#000",text:"Cashier"}],K="enum",z={mounted(e,o){k(e,o)},updated(e,o){k(e,o)}},k=(e,o)=>{var i;const{type:t,value:s,bold:n=!0}=o==null?void 0:o.value;if(typeof s>"u"||s==null)return;let r=N.find(l=>l.type==t&&l.value==s);r!=null?(e.style.color=r.color,e.style.fontWeight=n?"bold":"normal",e.innerText=b.global.t((i=r.text)!=null?i:t[s])):e.innerText=""};function G(e){e.directive(K,z)}const J=H(M);J.use(w).use(L).use(b).use(W).use(Y).use(G).mount("#app");export{m as E,Z as T,Q as __vite_legacy_guard,q as a,F as b,w as r};
