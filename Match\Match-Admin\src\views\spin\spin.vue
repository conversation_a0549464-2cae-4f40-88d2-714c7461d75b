<template>
    <div class="page-body flex-row">
        <div class="left-panel">
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Sector</h3>
                </div>
                <div class="panel-body">
                    <div class="sector">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'Sector')" v-text="item.optionName"
                            :key="key" :class="item.style"></div>
                    </div>
                </div>
            </div>

            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Exact Number</h3>
                </div>
                <div class="panel-body exact-number flex-row">
                    <div class="zero">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName == '0')"
                            v-text="item.optionName" :key="key" :class="item.style"></div>
                    </div>
                    <div class="number">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName != '0')"
                            v-text="item.optionName" :key="key" :class="item.style"></div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Other</h3>
                </div>
                <div class="panel-body">
                    <div class="dozens">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'Dozens')" v-text="item.optionName"
                            :key="key" :class="item.style"></div>
                    </div>
                    <div class="other">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type?.startsWith('Other'))"
                            v-text="item.optionName" :key="key" :class="item.style"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="right-panel">
            <ticket :matchId="1001" :selections="state.selections" :selected-option="selectedOption"></ticket>
        </div>
        <div class="right-panel" style="display: none;">
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Ticket</h3>
                </div>
                <div class="panel-body">
                    <table class="betting-table">
                        <thead>
                            <tr>
                                <th style="width: 15%;">ID</th>
                                <th style="width: 30%;">Stake</th>
                                <th style="width: 30%;">Winner</th>
                                <th style="width: 25%;">Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr v-for="(item, idx) in state.selections">
                                <td style="width: 15%;">{{ idx + 1 }}</td>
                                <td style="width: 30%;">
                                    {{ $numeral(item.stake) }}
                                </td>
                                <td style="width: 30%;">{{ item.optionName }}
                                    <span class="type">({{ getTypeText(item.type) }})</span>
                                </td>
                                <td style="width: 25%;">
                                    <SvgIcon name="close" className="icon-close" @click="deleteSelection(item, idx)">
                                    </SvgIcon>
                                </td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td>Stake</td>
                                <td class="text-right" colspan="3">TZS {{ $numeral(stake) }}</td>
                            </tr>
                            <tr>
                                <td>Max Payout</td>
                                <td class="text-right" colspan="3">TZS {{ $numeral(maxPayout) }}</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="panel-footer">
                    <div class="options chip">
                        <div class="item" @click="onChipClick(500)">500</div>
                        <div class="item" @click="onChipClick(1000)">1,000</div>
                        <div class="item" @click="onChipClick(1500)">1,500</div>
                        <div class="item" @click="onChipClick(5000)">5,000</div>
                    </div>
                </div>
            </div>

            <div class="panel">
                <div class="panel-body justify-between">
                    <el-button style="width: 150px;" plain type="danger" size="large" @click="clearSelections">Clear
                        Ticket</el-button>
                    <el-button style="width: 150px;" type="primary" size="large" @click="submit">Print
                        Ticket</el-button>
                </div>
            </div>
        </div>
    </div>
</template>


<style scoped lang="scss">

</style>


<style scoped lang="scss">
.page-body {
    max-width: 1000px;

    .right-panel {
        margin-left: 10px;
        max-width: 400px;
    }
}



.options {
    display: flex;
    margin-bottom: 8px;
    justify-content: flex-start;
    gap: 20px;

    &.chip {
        .item {
            width: auto;
            border-radius: 1000px;
            width: 80px;
            height: 80px;
            line-height: 80px;
            padding: 0;
            font-weight: bold;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.25);
            font-size: 18px;
            background-color: #d0daed;
        }
    }

    .item {
        width: 60px;
        border: 1px solid #f5f5f5;
        padding: 8px;
        text-align: center;
        border-radius: 6px;
        color: #1d1c1c;
        font-weight: 500;
        background-color: #eef3fc;
        cursor: pointer;
        user-select: none;

        &.active {
            background-color: #d8e4f8;
            border-color: transparent;
            color: #2160c4;
            font-weight: bold
        }
    }
}
</style>

<script setup lang="ts">
import OrderService from '@/api/order';
import matchOptions from './option';

import Ticket from '../components/ticket.vue';


import usePrinter from '@/hooks/usePrinter';
import { useResettableReactive } from '@/hooks/useResettableReactive';

interface StakeOrder {
    branchId: number;
    branchName: string;
    cashier: string;
    round: string;
    matchId: number;
    selections: {
        stake: number;
        payout: number;
        odds: number;
        optionId: number;
        optionName: string;
        type: string;
    }[]
}


const [state, resetState] = useResettableReactive<StakeOrder>({
    branchId: 0,
    branchName: 'TEST',
    cashier: '',
    round: '',
    matchId: 1003,
    selections: []
})

const [selectedOption, resetSelectedOption] = useResettableReactive({
    optionId: 0,
    optionName: '',
    odds: 0,
    stake: 0,
    type: ''
});


const onOptionClick = (e: MatchOption) => {
    selectedOption.optionId = e.id;
    selectedOption.odds = e.odds;
    selectedOption.optionName = e.optionName;
    selectedOption.type = e.type!;
}

const deleteSelection = (item: any, idx: number) => {
    state.selections.splice(idx, 1);
}

const clearSelections = () => {
    state.selections = [];
}

const getTypeText = (type: string) => {

    if (type == 'LOW_HIGH_EVEN_ODD') {
        return 'WINNER';
    }
    if (type == '1ST_OR_2ND') {
        return '1st or 2nd';
    }
    return type;
}

const onChipClick = (value: number) => {
    if (selectedOption.optionId == 0) {
        return;
    }
    selectedOption.stake = value;

    let item = state.selections.find(x => x.optionId == selectedOption.optionId);
    if (item != null) {
        item.stake += value;
        item.payout = item.stake * item.odds;
    } else {
        state.selections.push({
            stake: value,
            optionId: selectedOption.optionId,
            optionName: selectedOption.optionName,
            odds: selectedOption.odds,
            type: selectedOption.type,
            payout: value * selectedOption.odds,
        });
    }
    resetSelectedOption();
}


const submit = () => {
    if (state.selections.length == 0) {
        return;
    }


    OrderService.submit(state).then(res => {
        ElMessage({
            message: 'Order confirmed, please wait for printing.',
            type: 'success',
            plain: true,
        })
        clearSelections();
        usePrinter(res.result);
    });
}




const stake = computed(() => {
    return state.selections.reduce((acc, cur) => acc + cur.stake, 0);
})
const maxPayout = computed(() => {
    return state.selections.reduce((acc, cur) => acc + cur.payout, 0);
})


</script>