<template>
    <div class="nav-header justify-between">
        <div>
            <span> Hello {{ profileModel?.nickName }}</span>
        </div>
        <div>
            <span class="logout" @click="logout">退出登录</span>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useGlobalStore } from '@/store/global';

const { profileModel } = useGlobalStore();

const logout = () => {
    ElMessageBox.confirm('确认退出登录吗?', '提示', { type: 'warning' })
        .then(() => {
            useGlobalStore().userLogout();
        })
        .catch(_error => { });
}

</script>

<style scoped lang="scss">
.nav-header {
    height: 50px;
    line-height: 50px;
    background-color: #f5f5f5;
    padding: 0 20px;
    font-size: 14px;
}

.logout {
    color: #3273dc;
    cursor: pointer;
}
</style>