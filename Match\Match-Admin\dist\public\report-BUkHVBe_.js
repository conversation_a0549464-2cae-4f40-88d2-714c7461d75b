import{d as $,O as q,z as b,A as O,B as R,o as s,l as p,p as u,a,w as n,u as t,P as G,L as H,T as Q,Q as W,F as h,C as g,v as i,t as v,G as j,H as J,U as K,x as X,S as Z,c as I,N as ee}from"./.pnpm-DuVJJfpW.js";import{A as M,B as ae}from"./branch-zzdSpi4p.js";import{h as le}from"./http-QDC4lyIP.js";import{a as te}from"./index-Cc7WJN9q.js";import{g as oe}from"./option-CWv3DsyR.js";class ne{static query(l){return le.post("/api/report/query",l)}}const re={class:"page-body flex-col"},se={class:"panel"},de={class:"panel-body"},ue={class:"panel"},pe={class:"panel-body"},_e=$({__name:"report",setup(P){const l=q({agentId:"",branchId:"",cashierId:"",matchId:"",startDate:"",endDate:"",dateType:10}),S=oe(te),c=b(!1),V=b([]),B=()=>{c.value=!0,ne.query(l).then(r=>{V.value=r.result}).finally(()=>{c.value=!1})},D=O("searchForm"),U=()=>{var r;(r=D.value)==null||r.resetFields(),V.value=[]},F=()=>{l.branchId="",l.cashierId="",T()},N=()=>{l.cashierId="",w()},y=b([]),T=()=>{ae.getPaginatedList({pageIndex:1,pageSize:9999,accountId:l.agentId}).then(r=>{y.value=r.items})},Y=b([]),w=()=>{M.getPaginatedList({pageIndex:1,pageSize:9999,branchId:l.branchId,accountType:30}).then(r=>{Y.value=r.items})},k=b([]),A=()=>{M.getPaginatedList({pageIndex:1,pageSize:9999,accountType:20}).then(r=>{k.value=r.items})};return R(()=>{A()}),(r,o)=>{const f=ee,_=j,d=J,C=K,E=X,L=H,m=Z,x=Q,z=W;return s(),p("div",re,[u("div",se,[o[8]||(o[8]=u("div",{class:"panel-header with-border"},[u("div",{class:"panel-title"},"报表查询")],-1)),u("div",de,[a(L,{inline:!0,ref_key:"searchForm",ref:D,model:t(l),class:"search-form"},{default:n(()=>[a(d,{label:"代理",prop:"agentId"},{default:n(()=>[a(_,{placeholder:"",clearable:"",modelValue:t(l).agentId,"onUpdate:modelValue":o[0]||(o[0]=e=>t(l).agentId=e),onChange:F},{default:n(()=>[(s(!0),p(h,null,g(t(k),e=>(s(),I(f,{label:e.nickName,value:e.id},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"店铺",prop:"branchId"},{default:n(()=>[a(_,{placeholder:"",clearable:"",modelValue:t(l).branchId,"onUpdate:modelValue":o[1]||(o[1]=e=>t(l).branchId=e),onChange:N},{default:n(()=>[(s(!0),p(h,null,g(t(y),e=>(s(),I(f,{label:e.branchName,value:e.id},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"收银员",prop:"cashierId"},{default:n(()=>[a(_,{placeholder:"",clearable:"",modelValue:t(l).cashierId,"onUpdate:modelValue":o[2]||(o[2]=e=>t(l).cashierId=e)},{default:n(()=>[(s(!0),p(h,null,g(t(Y),e=>(s(),I(f,{label:e.nickName,value:e.id},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"游戏",prop:"matchId"},{default:n(()=>[a(_,{placeholder:"",clearable:"",modelValue:t(l).matchId,"onUpdate:modelValue":o[3]||(o[3]=e=>t(l).matchId=e)},{default:n(()=>[(s(!0),p(h,null,g(t(S),e=>(s(),I(f,{label:e.label,value:e.value},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"开始时间",prop:"startDate"},{default:n(()=>[a(C,{type:"date",format:"YYYY-MM-DD",clearable:"","value-format":"YYYY-MM-DD",modelValue:t(l).startDate,"onUpdate:modelValue":o[4]||(o[4]=e=>t(l).startDate=e)},null,8,["modelValue"])]),_:1}),a(d,{label:"结束时间",prop:"endDate"},{default:n(()=>[a(C,{type:"date",format:"YYYY-MM-DD",clearable:"","value-format":"YYYY-MM-DD",modelValue:t(l).endDate,"onUpdate:modelValue":o[5]||(o[5]=e=>t(l).endDate=e)},null,8,["modelValue"])]),_:1}),a(d,null,{default:n(()=>[a(E,{type:"primary",onClick:B,disabled:t(c)},{default:n(()=>o[6]||(o[6]=[i("查询")])),_:1},8,["disabled"]),a(E,{type:"danger",plain:"",onClick:U,disabled:t(c)},{default:n(()=>o[7]||(o[7]=[i("重置")])),_:1},8,["disabled"])]),_:1})]),_:1},8,["model"])])]),u("div",ue,[o[9]||(o[9]=u("div",{class:"panel-header with-border"},[u("div",{class:"panel-title"},"统计结果")],-1)),G((s(),p("div",pe,[a(x,{border:"",data:t(V)},{default:n(()=>[a(m,{label:"游戏",prop:"matchName"}),a(m,{label:"营业额",prop:"totalStake"},{default:n(({row:e})=>[i(v(r.$numeral(e.totalStake)),1)]),_:1}),a(m,{label:"赔付额",prop:"totalPayout"},{default:n(({row:e})=>[i(v(r.$numeral(e.totalPayout)),1)]),_:1}),a(m,{label:"已兑奖",prop:"totalPaid"},{default:n(({row:e})=>[i(v(r.$numeral(e.totalPaid)),1)]),_:1}),a(m,{label:"未兑奖",prop:"totalUnPaid"},{default:n(({row:e})=>[i(v(r.$numeral(e.totalUnPaid)),1)]),_:1})]),_:1},8,["data"])])),[[z,t(c)]])])])}}});export{_e as default};
