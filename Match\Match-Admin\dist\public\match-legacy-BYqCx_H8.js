System.register(["./.pnpm-legacy-DHcv0MF7.js","./io-table-legacy-BccT5s4C.js","./http-legacy-BkRyiHlu.js","./_plugin-vue_export-helper-legacy-DgAO6S8O.js","./index-legacy-C9rPDH0t.js"],(function(e,l){"use strict";var a,t,u,o,d,n,s,i,r,m,p,c,V,f,v,_,g,b,h,y,j,k,R,U,x,C,N,w,M,S,T,D,q;return{setters:[e=>{a=e.d,t=e.z,u=e.o,o=e.c,d=e.w,n=e.p,s=e.a,i=e.v,r=e.u,m=e.D,p=e.J,c=e.H,V=e.I,f=e.V,v=e.W,_=e.L,g=e.x,b=e.M,h=e.Y,y=e._,j=e.O,k=e.A,R=e.B,U=e.l,x=e.P,C=e.F,N=e.Q,w=e.t,M=e.R,S=e.S,T=e.Z},e=>{D=e._},e=>{q=e.h},null,null],execute:function(){class l{static getMatches(e){return q.get("/api/match/list",{params:e})}static update(e){return q.post("/api/match/update",e)}}const K={class:"justify-center"},z=a({__name:"detail-module",emits:["saved"],setup(e,{expose:a,emit:h}){const y=t(!1),j=t(!1),k=h,R=t({}),U=()=>{l.update(R.value).then((e=>{j.value=!1,k("saved",R.value)}))},x=()=>{j.value=!1};return a({open:e=>{j.value=!0,R.value=e}}),(e,l)=>{const a=p,t=c,h=V,k=f,C=v,N=_,w=g,M=b;return u(),o(M,{modelValue:r(j),"onUpdate:modelValue":l[5]||(l[5]=e=>m(j)?j.value=e:null),title:"游戏信息",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:d((()=>[n("div",K,[s(w,{type:"danger",disabled:r(y),plain:"",onClick:x},{default:d((()=>l[8]||(l[8]=[i("取消")]))),_:1},8,["disabled"]),s(w,{type:"primary",disabled:r(y),onClick:U},{default:d((()=>l[9]||(l[9]=[i("保存")]))),_:1},8,["disabled"])])])),default:d((()=>[s(N,{"label-width":"100px"},{default:d((()=>[s(t,{label:"游戏名",required:""},{default:d((()=>[s(a,{placeholder:"",modelValue:r(R).matchName,"onUpdate:modelValue":l[0]||(l[0]=e=>r(R).matchName=e)},null,8,["modelValue"])])),_:1}),s(t,{label:"时间间隔",required:""},{default:d((()=>[s(h,{placeholder:"",min:60,max:1800,modelValue:r(R).interval,"onUpdate:modelValue":l[1]||(l[1]=e=>r(R).interval=e),step:10,precision:0},null,8,["modelValue"])])),_:1}),s(t,{label:"开启时间",required:""},{default:d((()=>[s(a,{placeholder:"",modelValue:r(R).startTime,"onUpdate:modelValue":l[2]||(l[2]=e=>r(R).startTime=e)},null,8,["modelValue"])])),_:1}),s(t,{label:"结束时间",required:""},{default:d((()=>[s(a,{placeholder:"",modelValue:r(R).endTime,"onUpdate:modelValue":l[3]||(l[3]=e=>r(R).endTime=e)},null,8,["modelValue"])])),_:1}),s(t,{label:"状态",required:""},{default:d((()=>[s(C,{modelValue:r(R).status,"onUpdate:modelValue":l[4]||(l[4]=e=>r(R).status=e)},{default:d((()=>[s(k,{value:1},{default:d((()=>l[6]||(l[6]=[i("启用下单")]))),_:1}),s(k,{value:0},{default:d((()=>l[7]||(l[7]=[i("禁用下单")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["modelValue"])}}});class L{static update(e){return q.post("/api/config/update",e)}static get(e){return q.post("/api/config/get",e)}}const J={class:"justify-center"},O=a({__name:"config-module",emits:["saved"],setup(e,{expose:l,emit:a}){const f=t(!1),v=t(!1),j=t({}),k=a,R=()=>{const e=h.cloneDeep(j.value);e.jsonValue.reserveRatio&&(e.jsonValue.reserveRatio=e.jsonValue.reserveRatio/100),e.configValue=JSON.stringify(e.jsonValue),L.update(e).then((l=>{v.value=!1,k("saved",e)}))},U=()=>{v.value=!1};return l({open:e=>{(e=>{const l={configKey:"",matchName:e.matchName};switch(e.id){case 1e3:l.configKey="LuckyRouletten_Rule";break;case 2e3:l.configKey="CarsRacing_Rule";break;case 3e3:l.configKey="ColorLucky_Rule"}j.value.matchName=e.matchName,L.get(l).then((l=>{j.value=l.result,j.value.jsonValue=JSON.parse(l.result.configValue),j.value.jsonValue.reserveRatio&&(j.value.jsonValue.reserveRatio=100*j.value.jsonValue.reserveRatio),j.value.matchName=e.matchName,v.value=!0}))})(e)}}),(e,l)=>{const a=p,t=c,h=y,k=V,x=_,C=g,N=b;return u(),o(N,{modelValue:r(v),"onUpdate:modelValue":l[9]||(l[9]=e=>m(v)?v.value=e:null),title:"游戏配置",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:d((()=>[n("div",J,[s(C,{type:"danger",disabled:r(f),plain:"",onClick:U},{default:d((()=>l[10]||(l[10]=[i("取消")]))),_:1},8,["disabled"]),s(C,{type:"primary",disabled:r(f),onClick:R},{default:d((()=>l[11]||(l[11]=[i("保存")]))),_:1},8,["disabled"])])])),default:d((()=>[s(x,{"label-width":"100px"},{default:d((()=>[s(t,{label:"游戏名"},{default:d((()=>[s(a,{placeholder:"",readonly:"",modelValue:r(j).matchName,"onUpdate:modelValue":l[0]||(l[0]=e=>r(j).matchName=e)},null,8,["modelValue"])])),_:1}),"ColorLucky_Rule"==r(j).configKey?(u(),o(t,{key:0,label:"开奖配置"},{default:d((()=>[s(h,{label:"8中8",modelValue:r(j).jsonValue["8_8"],"onUpdate:modelValue":l[1]||(l[1]=e=>r(j).jsonValue["8_8"]=e)},null,8,["modelValue"]),s(h,{label:"8中7",modelValue:r(j).jsonValue["8_7"],"onUpdate:modelValue":l[2]||(l[2]=e=>r(j).jsonValue["8_7"]=e)},null,8,["modelValue"]),s(h,{label:"7中7",modelValue:r(j).jsonValue["7_7"],"onUpdate:modelValue":l[3]||(l[3]=e=>r(j).jsonValue["7_7"]=e)},null,8,["modelValue"]),s(h,{label:"7中6",modelValue:r(j).jsonValue["7_6"],"onUpdate:modelValue":l[4]||(l[4]=e=>r(j).jsonValue["7_6"]=e)},null,8,["modelValue"]),s(h,{label:"6中6",modelValue:r(j).jsonValue["6_6"],"onUpdate:modelValue":l[5]||(l[5]=e=>r(j).jsonValue["6_6"]=e)},null,8,["modelValue"]),s(h,{label:"6中5",modelValue:r(j).jsonValue["6_5"],"onUpdate:modelValue":l[6]||(l[6]=e=>r(j).jsonValue["6_5"]=e)},null,8,["modelValue"]),s(h,{label:"5中5",modelValue:r(j).jsonValue["5_5"],"onUpdate:modelValue":l[7]||(l[7]=e=>r(j).jsonValue["5_5"]=e)},null,8,["modelValue"])])),_:1})):(u(),o(t,{key:1,label:"吃分比例(%)"},{default:d((()=>[s(k,{min:-20,max:50,step:1,style:{width:"100%"},modelValue:r(j).jsonValue.reserveRatio,"onUpdate:modelValue":l[8]||(l[8]=e=>r(j).jsonValue.reserveRatio=e)},null,8,["modelValue"])])),_:1}))])),_:1})])),_:1},8,["modelValue"])}}}),I={class:"page-body flex-col"},P={class:"panel"},A={class:"panel-body",style:{padding:"0"}};e("default",a({__name:"match",setup(e){const a=j({pageIndex:1,pageSize:15,totalCount:0,totalPages:0}),m=k("DetailModuleRef"),p=k("ConfigModelRef"),c=t(!1),V=t([]),f=()=>{c.value=!0,l.getMatches({}).then((e=>{V.value=e.result})).finally((()=>{c.value=!1}))};return R((()=>{f()})),(e,l)=>{const t=S,v=g,_=T,b=D,y=N;return u(),U(C,null,[n("div",I,[n("div",P,[l[4]||(l[4]=n("div",{class:"panel-header with-border"},[n("div",{class:"panel-title"},"游戏列表")],-1)),x((u(),U("div",A,[s(b,{height:"500",data:r(V),pagination:r(a)},{default:d((()=>[s(t,{prop:"id",label:"序号"},{default:d((({row:e,$index:l})=>[i(w(l+1),1)])),_:1}),s(t,{prop:"",label:"",fixed:"right"},{default:d((({row:e})=>[s(v,{plain:"",type:"primary",size:"small",onClick:l=>(e=>{var l;null===(l=m.value)||void 0===l||l.open(h.cloneDeep(e))})(e)},{default:d((()=>l[0]||(l[0]=[i("编辑")]))),_:2},1032,["onClick"]),s(v,{plain:"",type:"primary",size:"small",onClick:l=>(e=>{var l;null===(l=p.value)||void 0===l||l.open(h.cloneDeep(e))})(e)},{default:d((()=>l[1]||(l[1]=[i("配置")]))),_:2},1032,["onClick"])])),_:1}),s(t,{prop:"matchName",label:"游戏名"}),s(t,{prop:"interval",label:"时间间隔(s)"}),s(t,{prop:"startTime",label:"开启时间"},{default:d((({row:e})=>[i(w(e.startTime)+" - "+w(e.endTime),1)])),_:1}),s(t,{prop:"status",label:"状态"},{default:d((({row:e})=>[0==e.status?(u(),o(_,{key:0,type:"danger"},{default:d((()=>l[2]||(l[2]=[i("禁用")]))),_:1})):M("",!0),1==e.status?(u(),o(_,{key:1,type:"success"},{default:d((()=>l[3]||(l[3]=[i("启用")]))),_:1})):M("",!0)])),_:1})])),_:1},8,["data","pagination"])])),[[y,r(c)]])])]),s(z,{ref:"DetailModuleRef",onSaved:f},null,512),s(O,{ref:"ConfigModelRef",onSaved:f},null,512)],64)}}}))}}}));
