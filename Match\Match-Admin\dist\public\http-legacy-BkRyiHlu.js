System.register(["./index-legacy-C9rPDH0t.js","./.pnpm-legacy-DHcv0MF7.js"],(function(e,r){"use strict";var t,s,a,o,n,i,c;return{setters:[e=>{t=e.T,s=e.r},e=>{a=e.a4,o=e.f,n=e.a5,i=e.a6,c=e.a7}],execute:function(){const r={401:e=>{n.error(e.data.message),s.replace("/login")},403:e=>{n.error(e.data.message)},409:e=>{n.error(e.data.message)},500:e=>{n.error(e.data.message)}},u={401:e=>{s.replace({path:"/login"})},403:e=>{n.error("没有权限访问")},404:e=>{n.error("请求的资源不存在")},500:e=>{n.error("服务器内部错误")},undefined:e=>{c({title:"Error",message:e.message,type:"error"})}},d=e("h",a.create({baseURL:"http://************:9002",timeout:1e4}));d.interceptors.request.use((e=>{if(e.skipRequestInterceptor)return e;const r=o.get(t);return r&&(e.headers.Authorization=`Bearer ${r}`),e}),(e=>(console.log("request error",e),Promise.reject(e)))),d.interceptors.response.use((e=>{if(e.config.skipResponseInterceptor)return e.data;if(e.data instanceof Blob)return e.data;const{code:t}=e.data;if(200===t)return e.data;const s=r[t];return s?s(e):n.error(e.data.message||"未知业务错误"),Promise.reject(e)}),(e=>{var r;if(i(e))return console.log("Request canceled",e.message),Promise.reject(e);const t=null===(r=e.response)||void 0===r?void 0:r.status;return(u[t]||u.undefined)(e),Promise.reject(e)}))}}}));
