<template>
    <div class="nav-header">
        <span> Hello Cashier</span> <b>(Branch-1)</b>
    </div>
    <div class="page-body">
        <div class="panel printer">
            <div class="panel-header with-border">
                <div class="panel-title">Printer</div>
            </div>
            <div class="panel-body">
                <h3 class="name">{{ printer?.name }}</h3>
                <div class="address">{{ printer?.address }}</div>
                <div class="mode">Mode: <span>{{ printer?.type }}</span></div>
            </div>
            <div class="panel-footer justify-between">
                <el-button type="warning">Change</el-button>
                <el-button type="primary" @click="printTestPage">Print Test Page</el-button>
                <el-button type="danger">Delete</el-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>

import { useGlobalStore } from '@/store/global';
import { storeToRefs } from 'pinia';

const globalStore = useGlobalStore();

const { printer } = storeToRefs(globalStore);


const printTestPage = () => {
    if (!window.ThermalPrinter) { 
        return;
    }

    window.ThermalPrinter.printFormattedTextAndCut(data, res => {
        console.log(res);
    }, error => {
        console.error(error);
    });
}



</script>

<style lang="scss" scoped>
.printer {
    .name {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .address {
        color: #868788;
        margin-bottom: 5px;
    }

    .mode {
        font-size: 16px;
    }
}
</style>