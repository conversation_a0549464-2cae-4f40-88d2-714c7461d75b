import { IOption } from "@/typings/typings";

export function getOptions(
  enums: any,
  option = { withAll: false, withEmpty: false, value: "", prefix: "" }
): IOption[] {
  let options: IOption[] = [];

  //自定义前缀 避免同单词不同翻译
  //eg:   prefix: 'UserType'  =>  UserType.Admin
  if (option?.prefix && option?.prefix.length > 0) {
    option.prefix = `${option.prefix}.`;
  }

  Object.keys(enums).forEach((e) => {
    if (isNaN(Number(e))) {
      options.push({ label: `${option.prefix}${e}`, value: enums[e] });
    }
  });

  if (option?.withAll) {
    options.unshift({ label: "All", value: option?.value });
  }
  if (option?.withEmpty) {
    options.unshift({ label: "", value: option?.value });
  }
  return options;
}

export function getOptionsWithEmpty(
  enums: any,
  value: string = "",
  prefix: string = ""
): IOption[] {
  return getOptions(enums, {
    withEmpty: true,
    withAll: false,
    value: value,
    prefix: prefix,
  });
}

export function getOptionsWithAll(
  enums: any,
  value: string = "",
  prefix: string = ""
): IOption[] {
  return getOptions(enums, {
    withEmpty: false,
    withAll: true,
    value: value,
    prefix: prefix,
  });
}
