System.register(["./.pnpm-legacy-DHcv0MF7.js","./_plugin-vue_export-helper-legacy-DgAO6S8O.js"],(function(e,a){"use strict";var t,n,i,o,p,g,l,r,u,s,d,c,x,v,m;return{setters:[e=>{t=e.d,n=e.A,i=e.o,o=e.l,p=e.a,g=e.w,l=e.$,r=e.a0,u=e.u,s=e.T,d=e.c,c=e.R,x=e.F,v=e.a1},e=>{m=e._}],execute:function(){var a=document.createElement("style");a.textContent=".pagination[data-v-8aac2784]{display:flex;justify-content:flex-end;margin-top:10px;margin-bottom:10px;margin-right:10px}\n/*$vite$:1*/",document.head.appendChild(a),e("_",m(t({__name:"io-table",props:{layout:{default:"total ,prev, pager, next, sizes, jumper"},pagination:{default:()=>({pageIndex:1,pageSize:10,totalCount:0,pageSizes:[10,20,50,100]})}},emits:["pager-change"],setup(e,{expose:a,emit:t}){const m=e,y=t,f=e=>{h(m.pagination.pageIndex,e)},z=e=>{h(e,m.pagination.pageSize)},h=(e,a)=>{m.pagination.pageIndex=e,m.pagination.pageSize=a,y("pager-change",{pageIndex:e,pageSize:a})},_=n("table");return a(new Proxy({},{get(e,a){var t;return null===(t=_.value)||void 0===t?void 0:t[a]},has:(e,a)=>!!_.value&&a in _.value})),(e,a)=>{const t=v;return i(),o(x,null,[p(u(s),r({"table-layout":"fixed",fit:"",stripe:"",ref:"table"},e.$attrs),{default:g((()=>[l(e.$slots,"default",{},void 0,!0)])),_:3},16),e.pagination.totalCount?(i(),d(t,{key:0,class:"pagination",background:"",layout:m.layout,"current-page":e.pagination.pageIndex,"page-sizes":[1,2,50,100],"page-size":e.pagination.pageSize,total:e.pagination.totalCount,onSizeChange:f,onCurrentChange:z},null,8,["layout","current-page","page-size","total"])):c("",!0)],64)}}}),[["__scopeId","data-v-8aac2784"]]))}}}));
