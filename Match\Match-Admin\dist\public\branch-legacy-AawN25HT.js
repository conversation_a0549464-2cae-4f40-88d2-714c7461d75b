!function(){function e(e,l){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);l&&(t=t.filter((function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),a.push.apply(a,t)}return a}function l(l){for(var t=1;t<arguments.length;t++){var d=null!=arguments[t]?arguments[t]:{};t%2?e(Object(d),!0).forEach((function(e){a(l,e,d[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(d)):e(Object(d)).forEach((function(e){Object.defineProperty(l,e,Object.getOwnPropertyDescriptor(d,e))}))}return l}function a(e,l,a){return(l=function(e){var l=function(e,l){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,l||"default");if("object"!=typeof t)return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===l?String:Number)(e)}(e,"string");return"symbol"==typeof l?l:l+""}(l))in e?Object.defineProperty(e,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[l]=a,e}System.register(["./.pnpm-legacy-DHcv0MF7.js","./io-table-legacy-BccT5s4C.js","./branch-legacy-CBy-yfTD.js","./index-legacy-C9rPDH0t.js","./_plugin-vue_export-helper-legacy-DgAO6S8O.js","./http-legacy-BkRyiHlu.js"],(function(e,a){"use strict";var t,d,o,u,r,n,s,i,c,p,m,f,b,v,g,y,h,_,V,N,w,j,k,O,P,C,x,U,I,S,D,M,F,z,R,T;return{setters:[e=>{t=e.d,d=e.z,o=e.A,u=e.B,r=e.o,n=e.c,s=e.w,i=e.p,c=e.a,p=e.v,m=e.u,f=e.l,b=e.C,v=e.F,g=e.D,y=e.J,h=e.H,_=e.G,V=e.V,N=e.W,w=e.L,j=e.x,k=e.M,O=e.N,P=e.O,C=e.P,x=e.Q,U=e.t,I=e.R,S=e.Y,D=e.S,M=e.Z},e=>{F=e._},e=>{z=e.B,R=e.A},e=>{T=e.E},null,null],execute:function(){const a={class:"justify-center"},q=t({__name:"detail-module",emits:["saved"],setup(e,{expose:l,emit:t}){const P=d(!1),C=d(!1),x=t,U=d({status:10}),I=o("form"),S=()=>{var e;null===(e=I.value)||void 0===e||e.validate(((e,l)=>{e?(P.value=!0,z.update(U.value).then((e=>{C.value=!1,x("saved",U.value)})).finally((()=>{P.value=!1}))):P.value=!1}))},D=()=>{var e;null===(e=I.value)||void 0===e||e.resetFields()},M=()=>{C.value=!1},F=d([]);return u((()=>{R.getPaginatedList({pageIndex:1,pageSize:9999,accountType:T.Agent}).then((e=>{F.value=e.items}))})),l({open:e=>{C.value=!0,U.value=e}}),(e,l)=>{const t=y,d=h,o=O,u=_,x=V,z=N,R=w,T=j,q=k;return r(),n(q,{modelValue:m(C),"onUpdate:modelValue":l[5]||(l[5]=e=>g(C)?C.value=e:null),title:"店铺信息",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1,onClosed:D},{footer:s((()=>[i("div",a,[c(T,{type:"danger",disabled:m(P),plain:"",onClick:M},{default:s((()=>l[8]||(l[8]=[p("取消")]))),_:1},8,["disabled"]),c(T,{type:"primary",disabled:m(P),onClick:S},{default:s((()=>l[9]||(l[9]=[p("保存")]))),_:1},8,["disabled"])])])),default:s((()=>[c(R,{"label-width":"100px",ref_key:"form",ref:I,model:m(U),"show-message":!1},{default:s((()=>[c(d,{label:"名称",required:"",prop:"branchName"},{default:s((()=>[c(t,{placeholder:"",modelValue:m(U).branchName,"onUpdate:modelValue":l[0]||(l[0]=e=>m(U).branchName=e)},null,8,["modelValue"])])),_:1}),c(d,{label:"联系人",prop:"contactName"},{default:s((()=>[c(t,{placeholder:"",modelValue:m(U).contactName,"onUpdate:modelValue":l[1]||(l[1]=e=>m(U).contactName=e)},null,8,["modelValue"])])),_:1}),c(d,{label:"地址",prop:"address"},{default:s((()=>[c(t,{placeholder:"",modelValue:m(U).address,"onUpdate:modelValue":l[2]||(l[2]=e=>m(U).address=e)},null,8,["modelValue"])])),_:1}),c(d,{label:"所属代理商",required:"",prop:"accountId"},{default:s((()=>[c(u,{placeholder:"",clearable:"",modelValue:m(U).accountId,"onUpdate:modelValue":l[3]||(l[3]=e=>m(U).accountId=e)},{default:s((()=>[(r(!0),f(v,null,b(m(F),(e=>(r(),n(o,{label:e.nickName,value:e.id},null,8,["label","value"])))),256))])),_:1},8,["modelValue"])])),_:1}),c(d,{label:"状态"},{default:s((()=>[c(z,{modelValue:m(U).status,"onUpdate:modelValue":l[4]||(l[4]=e=>m(U).status=e)},{default:s((()=>[c(x,{value:10},{default:s((()=>l[6]||(l[6]=[p("启用")]))),_:1}),c(x,{value:20},{default:s((()=>l[7]||(l[7]=[p("禁用")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])}}}),A={class:"justify-center"},E=t({__name:"create-module",emits:["saved"],setup(e,{expose:l,emit:a}){const t=d(!1),P=d(!1),C=a,x=d({status:10}),U=o("form"),I=()=>{var e;null===(e=U.value)||void 0===e||e.validate(((e,l)=>{e?(t.value=!0,z.create(x.value).then((e=>{P.value=!1,C("saved",x.value)})).finally((()=>{t.value=!1}))):t.value=!1}))},S=()=>{var e;null===(e=U.value)||void 0===e||e.resetFields()},D=()=>{P.value=!1},M=d([]);return u((()=>{R.getPaginatedList({pageIndex:1,pageSize:9999,accountType:T.Agent}).then((e=>{M.value=e.items}))})),l({open:()=>{P.value=!0}}),(e,l)=>{const a=y,d=h,o=O,u=_,C=V,F=N,z=w,R=j,T=k;return r(),n(T,{modelValue:m(P),"onUpdate:modelValue":l[5]||(l[5]=e=>g(P)?P.value=e:null),title:"店铺信息",width:"600",draggable:"","close-on-click-modal":!1,"close-on-press-escape":!1,onClosed:S},{footer:s((()=>[i("div",A,[c(R,{type:"danger",disabled:m(t),plain:"",onClick:D},{default:s((()=>l[8]||(l[8]=[p("取消")]))),_:1},8,["disabled"]),c(R,{type:"primary",disabled:m(t),onClick:I},{default:s((()=>l[9]||(l[9]=[p("保存")]))),_:1},8,["disabled"])])])),default:s((()=>[c(z,{"label-width":"100px",ref_key:"form",ref:U,model:m(x),"show-message":!1},{default:s((()=>[c(d,{label:"名称",required:"",prop:"branchName"},{default:s((()=>[c(a,{placeholder:"",modelValue:m(x).branchName,"onUpdate:modelValue":l[0]||(l[0]=e=>m(x).branchName=e)},null,8,["modelValue"])])),_:1}),c(d,{label:"联系人",prop:"contactName"},{default:s((()=>[c(a,{placeholder:"",modelValue:m(x).contactName,"onUpdate:modelValue":l[1]||(l[1]=e=>m(x).contactName=e)},null,8,["modelValue"])])),_:1}),c(d,{label:"地址",prop:"address"},{default:s((()=>[c(a,{placeholder:"",modelValue:m(x).address,"onUpdate:modelValue":l[2]||(l[2]=e=>m(x).address=e)},null,8,["modelValue"])])),_:1}),c(d,{label:"所属代理商",required:"",prop:"accountId"},{default:s((()=>[c(u,{placeholder:"",clearable:"",modelValue:m(x).accountId,"onUpdate:modelValue":l[3]||(l[3]=e=>m(x).accountId=e)},{default:s((()=>[(r(!0),f(v,null,b(m(M),(e=>(r(),n(o,{label:e.nickName,value:e.id},null,8,["label","value"])))),256))])),_:1},8,["modelValue"])])),_:1}),c(d,{label:"状态"},{default:s((()=>[c(F,{modelValue:m(x).status,"onUpdate:modelValue":l[4]||(l[4]=e=>m(x).status=e)},{default:s((()=>[c(C,{value:10},{default:s((()=>l[6]||(l[6]=[p("启用")]))),_:1}),c(C,{value:20},{default:s((()=>l[7]||(l[7]=[p("禁用")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])}}}),L={class:"page-body flex-col"},B={class:"panel"},$={class:"panel-body"},G={class:"panel"},H={class:"panel-header with-border"},J={class:"panel-tools"},Q={class:"panel-body",style:{padding:"0"}};e("default",t({__name:"branch",setup(e){const a=P({branchName:"",contactName:"",address:""}),t=P({pageIndex:1,pageSize:15,totalCount:0,totalPages:0}),b=()=>{t.pageIndex=1,T()},g=o("searchForm"),_=()=>{var e;null===(e=g.value)||void 0===e||e.resetFields(),b()},V=o("DetailModuleRef"),N=o("CreateModuleRef"),k=()=>{var e;null===(e=N.value)||void 0===e||e.open()},O=d(!1),R=d([]),T=()=>{O.value=!0,z.getPaginatedList(l(l({},a),t)).then((e=>{R.value=e.items,t.pageIndex=e.pageIndex,t.totalCount=e.totalCount,t.totalPages=e.totalPages})).finally((()=>{O.value=!1}))};return u((()=>{T()})),(e,l)=>{const d=y,o=h,u=j,N=w,P=D,z=M,A=F,W=x;return r(),f(v,null,[i("div",L,[i("div",B,[l[5]||(l[5]=i("div",{class:"panel-header with-border"},[i("div",{class:"panel-title"},"查询条件")],-1)),i("div",$,[c(N,{inline:!0,ref_key:"searchForm",ref:g,model:m(a),class:"search-form"},{default:s((()=>[c(o,{label:"名称",prop:"branchName"},{default:s((()=>[c(d,{placeholder:"",clearable:"",modelValue:m(a).branchName,"onUpdate:modelValue":l[0]||(l[0]=e=>m(a).branchName=e),modelModifiers:{trim:!0}},null,8,["modelValue"])])),_:1}),c(o,{label:"联系人",prop:"contactName"},{default:s((()=>[c(d,{placeholder:"",clearable:"",modelValue:m(a).contactName,"onUpdate:modelValue":l[1]||(l[1]=e=>m(a).contactName=e),modelModifiers:{trim:!0}},null,8,["modelValue"])])),_:1}),c(o,{label:"地址",prop:"address"},{default:s((()=>[c(d,{placeholder:"",clearable:"",modelValue:m(a).address,"onUpdate:modelValue":l[2]||(l[2]=e=>m(a).address=e),modelModifiers:{trim:!0}},null,8,["modelValue"])])),_:1}),c(o,null,{default:s((()=>[c(u,{type:"primary",onClick:b,disabled:m(O)},{default:s((()=>l[3]||(l[3]=[p("查询")]))),_:1},8,["disabled"]),c(u,{type:"danger",plain:"",onClick:_,disabled:m(O)},{default:s((()=>l[4]||(l[4]=[p("重置")]))),_:1},8,["disabled"])])),_:1})])),_:1},8,["model"])])]),i("div",G,[i("div",H,[l[7]||(l[7]=i("div",{class:"panel-title"},"店铺列表",-1)),i("div",J,[c(u,{type:"primary",onClick:k},{default:s((()=>l[6]||(l[6]=[p("添加")]))),_:1})])]),C((r(),f("div",Q,[c(A,{height:"500",data:m(R),pagination:m(t),onPagerChange:T},{default:s((()=>[c(P,{prop:"id",label:"序号"},{default:s((({row:e,$index:l})=>[p(U(l+1),1)])),_:1}),c(P,{prop:"",label:"",width:"100",fixed:"right"},{default:s((({row:e})=>[c(u,{plain:"",type:"primary",size:"small",onClick:l=>(e=>{var l;null===(l=V.value)||void 0===l||l.open(S.cloneDeep(e))})(e)},{default:s((()=>l[8]||(l[8]=[p("编辑")]))),_:2},1032,["onClick"])])),_:1}),c(P,{prop:"branchName",label:"店铺名称"}),c(P,{prop:"contactName",label:"联系人"}),c(P,{prop:"address",label:"地址"}),c(P,{prop:"account.nickName",label:"所属代理商"}),c(P,{prop:"status",label:"状态"},{default:s((({row:e})=>[20==e.status?(r(),n(z,{key:0,type:"danger"},{default:s((()=>l[9]||(l[9]=[p("禁用")]))),_:1})):I("",!0),10==e.status?(r(),n(z,{key:1,type:"success"},{default:s((()=>l[10]||(l[10]=[p("启用")]))),_:1})):I("",!0)])),_:1}),c(P,{prop:"createTime",label:"添加时间"},{default:s((({row:l})=>[p(U(e.$moment(l.createTime)),1)])),_:1})])),_:1},8,["data","pagination"])])),[[W,m(O)]])])]),c(q,{ref:"DetailModuleRef",onSaved:T},null,512),c(E,{ref:"CreateModuleRef",onSaved:T},null,512)],64)}}}))}}}))}();
