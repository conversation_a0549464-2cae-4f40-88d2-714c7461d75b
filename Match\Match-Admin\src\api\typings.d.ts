
export interface AccountModel {
    id: number;
    loginAccount: string;
    loginPassword: string;
    nickName: string;
    endTime: string;
    mobile: string;
    status: number;
    balance: number;
    branchId: number;
    accountType: number;
    lastLoginIp: string;
    lastLoginTime: string;
    createBy: string;
    recordState: number;
    createTime: string;
    updateTime: string;
    branch?: BranchModel
}

export interface ReportModel {
    matchId: number;
    matchName: string;
    totalStake: number;
    totalPayout: number;
    totalPaid: number;
    totalUnPaid: number;
    profit: number;
    profitRate: number;
}

export interface ConfigModel {
    id: number;
    matchName?: string;
    configKey: string;
    configValue: string;
    jsonValue?: any
}


export interface AssetsLogModel {
    id: number;
    agentId?: number;
    formId: number;
    toId?: number | string;
    branchId?: number | string;
    balance?: number;
    remark?: string;
}

export interface TokenModel {
    token: string;
    expireAt: string;
}
export interface MatchModel {
    id: number;
    matchName: string;
    scheduleDate: string;
    startTime: string;
    endTime: string;
    interval: number;
    status: number;
}

export interface MatchOption {
    id: number;
    matchId: number;
    odds: number;
    optionName: string;
    style: string | null;
    type?: string;
    createTime?: string;
}

export interface BranchModel {
    id: number;
    branchName: string;
    address: string;
    contactName: string;
    status: number;
    accountId: number;
    endTime?: string;
    createTime: string;
}

export interface ProfileModel {
    id: number;
    branchId: number;
    branchName?: string;
    nickName: string;
    loginAccount: string;
    loginPassword?: string;
    salt?: string;
    loginAt: string;
    lastLoginIp: string;
    createTime: string;
    token: string;
    expireAt: string;
    accountType: string;
}

export interface OrderModel {
    id: string;
    branchId: number;
    branchName: string;
    cashier: string;
    orderNo: string;
    round: string;
    matchId: number;
    matchName: string;
    expireTime: string;
    maxPayout: number;
    actualPayout?: number;
    stake: number;
    status: number;
    payTime?: string;
    printTime: string;
    cancelTime?: string;
    createTime: string;
    items: Array<OrderItem>
}

export interface OrderItem {
    id: number;
    orderId: number;
    optionId: number;
    optionName: string;
    optionType: string;
    odds: number;
    payout: number;
    stake: number;
    type: string;
    status: number;
    createTime: string;
}


type PrinterModel = {
    id?: string | number,
    name?: string;
    address: string;
    type: "bluetooth" | "tcp" | "usb" | "";
    port: number;
    printerWidthMM: 72 | 48;
    printerNbrCharactersPerLine: 48 | 32;
    paperWidth?: 72 | 48
}



interface PaginationParams {
    pageIndex: number;
    pageSize: number;
}


interface Result<T> {
    result: T;
    message: string;
    code: number;
}

interface PaginatedList<T> {
    pageIndex: number;
    totalCount: number;
    totalPages: number;
    items: T[];
}
