<template>
    <div class="full-page console-page">
        <div class="console-nav">
            <div class="logo"></div>
            <div class="menu">
                <div class="item">
                    <router-link to="/dashboard" activeClass="active">
                        <SvgIcon name="home" className="icon-home"></SvgIcon>
                        <span>工作台</span>
                    </router-link>
                </div>

                <div class="item" v-if="profileModel?.accountType == 'Admin'">
                    <router-link to="/branch" activeClass="active">
                        <SvgIcon name="order" className="icon-order"></SvgIcon>
                        <span>店铺管理</span>
                    </router-link>
                </div>
                <div class="item">
                    <router-link to="/report" activeClass="active">
                        <SvgIcon name="order" className="icon-order"></SvgIcon>
                        <span>报表中心</span>
                    </router-link>
                </div>
                <div class="item" v-if="profileModel?.accountType == 'Admin'">
                    <router-link to="/assets" activeClass="active">
                        <SvgIcon name="order" className="icon-order"></SvgIcon>
                        <span>财务中心</span>
                    </router-link>
                </div>

                <div class="item" v-if="profileModel?.accountType == 'Admin'">
                    <router-link to="/match" activeClass="active">
                        <SvgIcon name="order" className="icon-order"></SvgIcon>
                        <span>游戏管理</span>
                    </router-link>
                </div>
                <div class="item" v-if="profileModel?.accountType == 'Admin'">
                    <router-link to="/account" activeClass="active">
                        <SvgIcon name="order" className="icon-order"></SvgIcon>
                        <span>账户中心</span>
                    </router-link>
                </div>
            </div>
            <!-- <div class="menu footer">
                <div class="item">
                    <router-link to="/printer" activeClass="active">
                        <SvgIcon name="setting" className="icon-setting"></SvgIcon>
                        <span>系统设置</span>
                    </router-link>
                </div>
            </div> -->
        </div>

        <div class="console-body">
            <navbar></navbar>
            <el-scrollbar class="page-body-scrollbar">
                <router-view v-slot="{ Component }">
                    <keep-alive>
                        <component :is="Component" :key="$route.fullPath" />
                    </keep-alive>
                </router-view>
            </el-scrollbar>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useGlobalStore } from '@/store/global';

import navbar from '../components/navbar.vue';

const { userLogout, profileModel } = useGlobalStore();


const logout = () => {
    ElMessageBox.confirm('You will lose your session after logging out.Continue?', 'Warning', { type: 'warning', confirmButtonText: 'Confirm' })
        .then(() => {
            userLogout();
        })
        .catch(_error => { });
}

</script>


<style scoped lang="scss">
.page-body-scrollbar {
    height: calc(100vh - 51px);
    overflow-y: hidden;
}
.svg-icon {
    font-size: 26px;
    color: rgba(255, 255, 255, 0.67);
    stroke: rgba(255, 255, 255, 0.67);
}

.console-page {
    display: flex;
}

.console-nav {
    flex-basis: 70px;
    flex-shrink: 0;
    background: #282931;
    position: relative;

    .logo {
        background: #f5f5f5;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        margin: 30px auto 30px;
        text-align: center;

        position: relative;
    }

    .menu {
        display: flex;
        flex-direction: column;
        text-align: center;
        margin-top: 10px;

        .item {
            // height: 75px;
            // line-height: 75px;


            span {
                font-size: 12px;
                display: block;
            }

            a {
                cursor: pointer;
                padding: 10px 0;
                color: rgba(255, 255, 255, 0.67);
                text-decoration: none;
                display: block;

                &:hover {
                    color: #fff;
                }
            }

            .active {
                background-color: #3273dc;

                a {
                    color: #fff;

                    .svg-icon {
                        color: #fff;
                        stroke: #fff;
                    }
                }
            }
        }
    }

    .footer {
        position: absolute;
        width: 100%;
        bottom: 0px;
        left: 0px;
    }
}

.console-body {
    border-top: 1px solid #ececec;
    flex-grow: 1;
    flex-basis: auto;
    height: 100vh;
    background-color: #ececec;
    overflow-y: auto;
    box-sizing: border-box  ;

    :deep(.nav-header) {
        height: 50px;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        padding-left: 20px;

        span {
            margin-right: 20px
        }
    }
}
</style>