<template>
    <div class="full-page login-page">
        <div class="login-body">
            <div class="login-title">
                Magic Admin
            </div>
            <el-form :model="formData" :show-message="false" class="login-form" ref="loginForm"
                @submit.native.prevent="login">
                <el-form-item label="" prop="loginAccount" class="input" required>
                    <el-input placeholder="username" v-model="formData.loginAccount" size="large" />
                </el-form-item>
                <el-form-item label="" prop="loginPassword" class="input" required>
                    <el-input placeholder="password" type="password" v-model="formData.loginPassword" size="large" />
                </el-form-item>
                <el-form-item class="input">
                    <el-button type="primary" native-type="submit" :disabled="disabled" :loading="loading"
                        class="full-width" size="large" @click="login"> Login
                    </el-button>
                </el-form-item>
            </el-form>

        </div>
    </div>
</template>
<script lang="ts" setup>
import { useGlobalStore } from '@/store/global';
import type { FormInstance } from 'element-plus';

const disabled = computed(() => {
    return formData.loginAccount === '' || formData.loginPassword === '';
})

const formData = reactive({
    loginAccount: '',
    loginPassword: ''
})

const loading = ref(false);

const { userLogin } = useGlobalStore();


const loginForm = useTemplateRef<FormInstance>('loginForm');

const login = async () => {

    loginForm.value?.validate(async (valid, _fields) => {
        if (!valid) {
            return;
        }
        try {
            loading.value = true;

            userLogin(formData);

        } finally {
            loading.value = false;
        }
    })


}

</script>

<style scoped lang="scss">
.login-page {
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-body {
    padding: 40px;
    width: 100%;
    max-width: 410px;
}

.login-title {
    margin-bottom: 10px;
    text-align: center;
    font-size: 40px;
    color: #2196F3;
    padding: 15px 0;
    font-weight: bold
}


.login-form {
    .input {
        margin-bottom: 20px;
        --el-component-size-large: 45px;
    }

    --el-button-size: 45px;
}
</style>