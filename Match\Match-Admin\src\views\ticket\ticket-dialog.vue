<template>
    <el-dialog v-model="visible" title="Ticket Detail" width="600" draggable @closed="close"
        :close-on-click-modal="false" :close-on-press-escape="false">
        <div v-loading="loading">
            <div class="form-row">
                <div class="form-item">
                    <span class="label">Name</span>
                    <span class="text"> {{ order.matchName }}</span>
                </div>
                <div class="form-item">
                    <span class="label">Barcode</span>
                    <span class="text"> {{ order.orderNo }}</span>
                </div>
                <div class="form-item">
                    <span class="label">Round</span>
                    <span class="text text-right"> {{ order.round }}</span>
                </div>
            </div>
            <div class="form-row">
                <div class="form-item">
                    <span class="label">Cashier</span>
                    <span class="text"> {{ order.cashier }}</span>
                </div>
                <div class="form-item">
                    <span class="label">Stake</span>
                    <span class="text text-right"> {{ $numeral(order.stake) }}</span>
                </div>
                <div class="form-item">
                    <span class="label">Max Payout</span>
                    <span class="text text-right"> {{ $numeral(order.maxPayout) }}</span>
                </div>
            </div>
            <div class="form-row">
                <div class="form-item">
                    <span class="label">Order Time</span>
                    <span class="text"> {{ $moment(order.createTime) }}</span>
                </div>
                <div class="form-item">
                    <span class="label">Actual Payout</span>
                    <span class="text act-payout"> {{ $numeral(order.actualPayout) }}</span>
                </div>
                <div class="form-item">
                    <template v-if="order.status == 10 || order.status == 11">
                        <span class="label">Cash Status</span>
                        <span class="text act-payout">No</span>
                    </template>
                    <template v-if="order.status == 40">
                        <span class="label">Cash Status</span>
                        <span class="text act-payout">Yes</span>
                    </template>
                </div>
            </div>
            <div class="form-row" v-if="order.status >= 30">
                <div class="form-item">
                    <span class="label">Cancel Time</span>
                    <span class="text"> {{ $moment(order.cancelTime) }}</span>
                </div>
                <div class="form-item">

                </div>
                <div class="form-item" v-if="order.payTime">
                    <span class="label">Cash Time</span>
                    <span class="text act-payout"> {{ $moment(order.payTime) }}</span>
                </div>
            </div>
            <el-scrollbar class="ticket-table">
                <table>
                    <thead>
                        <tr>
                            <th style="width: 50px;">SN</th>
                            <th style="width: 100px;">WINNER</th>
                            <th style="width: 50px;">STAKE</th>
                            <th style="width: 50px;">ODDS</th>
                            <th style="width: 50px;">PAYOUT</th>
                            <th style="width: 50px;">RESULT</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(row, index) in order.items" :key="index">
                            <td>{{ index + 1 }}</td>
                            <td>{{ row.optionName }} ({{ row.optionType }})</td>
                            <td>{{ $numeral(row.stake) }}</td>
                            <td>{{ row.odds }}</td>
                            <td>{{ $numeral(row.payout) }}</td>
                            <td>
                                <span v-if="row.status == 0">WAIT</span>
                                <span v-if="row.status == 10">WIN</span>
                                <span v-if="row.status == 20">LOSE</span>
                            </td>
                        </tr>

                    </tbody>
                    <!-- <tfoot>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>Actual</td>
                            <td>0</td>
                            <td></td>
                        </tr>
                    </tfoot> -->
                </table>
            </el-scrollbar>
        </div>

        <template #footer>
            <div class="justify-between">
                <div class="left">
                    <el-button type="success" :disabled="loading" plain v-if="order.status == 10 || order.status == 11"
                        @click="cash">Cash</el-button>
                    <el-button type="danger" :disabled="loading" plain @click="cancel"
                        v-if="order.status == 0">Cancel</el-button>
                </div>
                <div class="right">
                    <el-button type="primary" :disabled="loading" @click="refresh">Refresh</el-button>
                    <el-button type="primary" :disabled="loading" @click="close">Close</el-button>
                </div>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import OrderService from '@/api/order';
import { OrderModel } from '@/api/typings';
import Dialog from '@/utils/dialog';

const order = ref<OrderModel>({
    id: '',
    branchId: 0,
    branchName: '',
    cashier: '',
    orderNo: '',
    round: '',
    matchId: 0,
    matchName: '',
    expireTime: '',
    maxPayout: 0,
    stake: 0,
    status: 0,
    printTime: '',
    createTime: '',
    items: []
});

const loading = ref(false);
const visible = ref(false);

const open = (id: string) => {
    loading.value = true;
    visible.value = true;

    OrderService.query({ id }).then(res => {
        order.value = res.result;
    }).finally(() => {
        loading.value = false;
    });
};
const openWithOrder = (val: OrderModel) => {
    loading.value = false;
    visible.value = true;
    order.value = val;
}

const refresh = () => {
    loading.value = true;
    open(order.value.id);
};

const cancel = () => {
    Dialog.confirm('Are you sure to cancel this order?').then(() => {
        OrderService.cancel({ id: order.value.id }).then(res => {
            refresh();
        });
    });
};

const cash = () => {
    Dialog.confirm('Are you sure to cash this order?').then(() => {
        OrderService.cash({ id: order.value.id }).then(res => {
            refresh();
        });
    });
};


const close = () => {
    visible.value = false;
};

defineExpose({
    open,
    openWithOrder
});
</script>
<style lang="scss" scoped>
.form-row {
    margin-bottom: 8px;
    display: flex;
    flex-direction: row;
    color: #000;

    .form-item {
        width: 280px;
        flex-direction: row;

        .label {
            display: block;
            width: 100px;
            white-space: nowrap;
            font-weight: bold;
        }

        .text {
            width: 180px;
            display: block;
            padding: 5px;
            border: 1px solid #ebeef5;
        }
    }
}

.ticket-table {
    background-color: #fff;
    flex: 1;
    height: 200px;
    margin-top: 20px;
    border: 1px solid #ebeef5;

    table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed;
        color: #000;
    }

    thead {
        background-color: #f5f7fa;
        position: sticky;
        top: 0;
    }

    tfoot {
        background-color: #f5f7fa;
        position: sticky;
        bottom: -5px;
    }


    tbody {}

    th,
    td {
        padding: 5px 3px;
        border-bottom: 1px solid #ebeef5;
        text-align: left;
    }

    th {
        font-weight: bold;
    }
}

.act-payout {
    color: red;
    font-weight: bold;
    text-align: right;
}
</style>
