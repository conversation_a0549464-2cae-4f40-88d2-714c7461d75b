function r(f,l={withAll:!1,withEmpty:!1,value:"",prefix:""}){let e=[];return l!=null&&l.prefix&&(l==null?void 0:l.prefix.length)>0&&(l.prefix="".concat(l.prefix,".")),Object.keys(f).forEach(a=>{isNaN(Number(a))&&e.push({label:"".concat(l.prefix).concat(a),value:f[a]})}),l!=null&&l.withAll&&e.unshift({label:"All",value:l==null?void 0:l.value}),l!=null&&l.withEmpty&&e.unshift({label:"",value:l==null?void 0:l.value}),e}function u(f,l="",e=""){return r(f,{withEmpty:!1,withAll:!0,value:l,prefix:e})}export{r as a,u as g};
