System.register(["./_plugin-vue_export-helper-legacy-DgAO6S8O.js","./.pnpm-legacy-DHcv0MF7.js","./global-legacy-DKbonGJ6.js","./http-legacy-BkRyiHlu.js","./index-legacy-C9rPDH0t.js"],(function(e,a){"use strict";var o,l,n,t,s,i,c,r,d,v,u,f,m,p,g,x,h,b,k;return{setters:[e=>{o=e._},e=>{l=e.k,n=e.o,t=e.l,s=e.m,i=e.p,c=e.q,r=e.d,d=e.t,v=e.u,u=e.v,f=e.a,m=e.w,p=e.x,g=e.r,x=e.c,h=e.y,b=e.K},e=>{k=e.u},null,null],execute:function(){var a=document.createElement("style");a.textContent=".svg-icon[data-v-e42162f1]{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}.svg-external-icon[data-v-e42162f1]{background-color:currentColor;-webkit-mask-size:cover!important;mask-size:cover!important;display:inline-block}.svg-icon[data-v-7de44e9f]{font-size:26px;color:rgba(255,255,255,.67);stroke:rgba(255,255,255,.67)}.console-page[data-v-7de44e9f]{display:flex}.console-nav[data-v-7de44e9f]{flex-basis:70px;flex-shrink:0;background:#282931;position:relative}.console-nav .logo[data-v-7de44e9f]{background:#f5f5f5;border-radius:50%;width:45px;height:45px;margin:30px auto;text-align:center;position:relative}.console-nav .menu[data-v-7de44e9f]{display:flex;flex-direction:column;text-align:center;margin-top:10px}.console-nav .menu .item span[data-v-7de44e9f]{font-size:12px;display:block}.console-nav .menu .item a[data-v-7de44e9f]{cursor:pointer;padding:10px 0;color:rgba(255,255,255,.67);text-decoration:none;display:block}.console-nav .menu .item a[data-v-7de44e9f]:hover{color:#fff}.console-nav .menu .item .active[data-v-7de44e9f]{background-color:#3273dc}.console-nav .menu .item .active a[data-v-7de44e9f]{color:#fff}.console-nav .menu .item .active a .svg-icon[data-v-7de44e9f]{color:#fff;stroke:#fff}.console-nav .footer[data-v-7de44e9f]{position:absolute;width:100%;bottom:0;left:0}.console-body[data-v-7de44e9f]{border-top:1px solid #ececec;flex-grow:1;flex-basis:auto;height:100vh;background-color:#ececec;overflow-y:auto}.console-body[data-v-7de44e9f] .nav-header{height:50px;background:#fff;box-shadow:0 2px 4px rgba(0,0,0,.08);display:flex;align-items:center;padding-left:20px}.console-body[data-v-7de44e9f] .nav-header span{margin-right:20px}\n/*$vite$:1*/",document.head.appendChild(a);const y=["xlink:href"],_=o({__name:"SvgIcon",props:{name:{type:String,required:!0},className:{type:String,default:""}},setup(e){const a=e,o=l((()=>/^(https?:|mailto:|tel:)/.test(a.name))),r=l((()=>`#icon-${a.name}`)),d=l((()=>a.className?"svg-icon "+a.className:"svg-icon")),v=l((()=>({mask:`url(${a.name}) no-repeat 50% 50%`,"-webkit-mask":`url(${a.name}) no-repeat 50% 50%`})));return(e,a)=>o.value?(n(),t("div",{key:0,style:s(v.value),class:"svg-external-icon svg-icon"},null,4)):(n(),t("svg",{key:1,class:c(d.value),"aria-hidden":"true"},[i("use",{"xlink:href":r.value},null,8,y)],2))}},[["__scopeId","data-v-e42162f1"]]),w={class:"nav-header flex-between"},C=r({__name:"navbar",setup(e){const{profileModel:a}=k(),o=()=>{k().userLogout()};return(e,l)=>{var s,c;const r=p;return n(),t("div",w,[i("div",null,[i("span",null," Hello "+d(null===(s=v(a))||void 0===s?void 0:s.nickName),1),l[0]||(l[0]=u()),i("b",null,"("+d(null===(c=v(a))||void 0===c?void 0:c.branchName)+")",1)]),i("div",null,[f(r,{type:"danger",onClick:o},{default:m((()=>l[1]||(l[1]=[u("Logout")]))),_:1})])])}}}),N={class:"full-page console-page"},$={class:"console-nav"},j={class:"menu"},z={class:"item"},S={class:"item"},I={class:"item"},q={class:"item"},L={class:"item"},E={class:"item"},H={class:"console-body"};e("default",o(r({__name:"layout",setup:e=>(k(),(e,a)=>{const o=_,l=g("router-link"),s=g("router-view");return n(),t("div",N,[i("div",$,[a[6]||(a[6]=i("div",{class:"logo"},null,-1)),i("div",j,[i("div",z,[f(l,{to:"/dashboard",activeClass:"active"},{default:m((()=>[f(o,{name:"home",className:"icon-home"}),a[0]||(a[0]=i("span",null,"工作台",-1))])),_:1})]),i("div",S,[f(l,{to:"/branch",activeClass:"active"},{default:m((()=>[f(o,{name:"order",className:"icon-order"}),a[1]||(a[1]=i("span",null,"店铺管理",-1))])),_:1})]),i("div",I,[f(l,{to:"/report",activeClass:"active"},{default:m((()=>[f(o,{name:"order",className:"icon-order"}),a[2]||(a[2]=i("span",null,"报表中心",-1))])),_:1})]),i("div",q,[f(l,{to:"/assets",activeClass:"active"},{default:m((()=>[f(o,{name:"order",className:"icon-order"}),a[3]||(a[3]=i("span",null,"财务中心",-1))])),_:1})]),i("div",L,[f(l,{to:"/match",activeClass:"active"},{default:m((()=>[f(o,{name:"order",className:"icon-order"}),a[4]||(a[4]=i("span",null,"游戏管理",-1))])),_:1})]),i("div",E,[f(l,{to:"/account",activeClass:"active"},{default:m((()=>[f(o,{name:"order",className:"icon-order"}),a[5]||(a[5]=i("span",null,"账户中心",-1))])),_:1})])])]),i("div",H,[f(C),f(s,null,{default:m((({Component:a})=>[(n(),x(b,null,[(n(),x(h(a),{key:e.$route.fullPath}))],1024))])),_:1})])])})}),[["__scopeId","data-v-7de44e9f"]]))}}}));
