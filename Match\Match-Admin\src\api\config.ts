import http from '@/utils/http';
import { ConfigModel, MatchModel, Result } from './typings';

export default class ConfigService {

    public static update(data: ConfigModel) {
        return http.post<any, Result<ConfigModel>>("/api/config/update", data);
    }

    public static get(data: {
        configKey?: string
    }) {
        return http.post<any, Result<ConfigModel>>("/api/config/get", data);
    }
}